import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/utils/logger.dart';
import '../models/wallet_models.dart' as wallet_models;

// Wallet State
class WalletState {
  final Map<String, double> balances;
  final List<String> supportedCurrencies;
  final String? selectedCurrency;
  final bool isLoading;
  final bool isLoadingMore;
  final String? error;
  final Map<String, String> addresses;
  final List<wallet_models.Transaction> transactions;
  final List<wallet_models.WalletTransaction> transactionHistory;
  final bool hasMoreTransactions;

  const WalletState({
    this.balances = const {},
    this.supportedCurrencies = const ['BTC', 'ETH', 'MATIC', 'USDT', 'USDC'],
    this.selectedCurrency,
    this.isLoading = false,
    this.isLoadingMore = false,
    this.error,
    this.addresses = const {},
    this.transactions = const [],
    this.transactionHistory = const [],
    this.hasMoreTransactions = true,
  });

  WalletState copyWith({
    Map<String, double>? balances,
    List<String>? supportedCurrencies,
    String? selectedCurrency,
    bool? isLoading,
    bool? isLoadingMore,
    String? error,
    Map<String, String>? addresses,
    List<wallet_models.Transaction>? transactions,
    List<wallet_models.WalletTransaction>? transactionHistory,
    bool? hasMoreTransactions,
  }) {
    return WalletState(
      balances: balances ?? this.balances,
      supportedCurrencies: supportedCurrencies ?? this.supportedCurrencies,
      selectedCurrency: selectedCurrency ?? this.selectedCurrency,
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      error: error,
      addresses: addresses ?? this.addresses,
      transactions: transactions ?? this.transactions,
      transactionHistory: transactionHistory ?? this.transactionHistory,
      hasMoreTransactions: hasMoreTransactions ?? this.hasMoreTransactions,
    );
  }

  double getBalance(String currency) => balances[currency] ?? 0.0;
  String? getAddress(String currency) => addresses[currency];
  bool get hasError => error != null;
}

// Using Transaction model from wallet_models.dart

// Wallet Notifier
class WalletNotifier extends StateNotifier<WalletState> {
  WalletNotifier() : super(const WalletState()) {
    _initialize();
  }

  Future<void> _initialize() async {
    try {
      state = state.copyWith(isLoading: true);
      
      // Initialize wallet addresses
      final addresses = <String, String>{};
      for (final currency in state.supportedCurrencies) {
        addresses[currency] = _generateAddress(currency);
      }
      
      // Load balances (mock data for now)
      final balances = <String, double>{
        'BTC': 0.05432,
        'ETH': 1.2345,
        'MATIC': 150.0,
        'USDT': 500.0,
        'USDC': 250.0,
      };

      state = state.copyWith(
        addresses: addresses,
        balances: balances,
        selectedCurrency: 'BTC',
        isLoading: false,
      );

      AppLogger.info('Wallet initialized successfully');
    } catch (e) {
      AppLogger.error('Failed to initialize wallet', e);
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  String _generateAddress(String currency) {
    // Mock address generation
    switch (currency) {
      case 'BTC':
        return '**********************************';
      case 'ETH':
        return '******************************************';
      case 'MATIC':
        return '******************************************';
      case 'USDT':
        return '******************************************';
      case 'USDC':
        return '******************************************';
      default:
        return '******************************************';
    }
  }

  void selectCurrency(String currency) {
    if (state.supportedCurrencies.contains(currency)) {
      state = state.copyWith(selectedCurrency: currency);
      AppLogger.info('Selected currency: $currency');
    }
  }

  Future<void> refreshBalances() async {
    try {
      state = state.copyWith(isLoading: true);
      
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      // Mock updated balances
      final updatedBalances = Map<String, double>.from(state.balances);
      for (final currency in updatedBalances.keys) {
        updatedBalances[currency] = updatedBalances[currency]! * (0.95 + (0.1 * (DateTime.now().millisecondsSinceEpoch % 100) / 100));
      }

      state = state.copyWith(
        balances: updatedBalances,
        isLoading: false,
      );

      AppLogger.info('Balances refreshed');
    } catch (e) {
      AppLogger.error('Failed to refresh balances', e);
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<bool> sendTransaction({
    required String currency,
    required String toAddress,
    required double amount,
  }) async {
    try {
      state = state.copyWith(isLoading: true);

      // Validate balance
      final currentBalance = state.getBalance(currency);
      if (amount > currentBalance) {
        throw Exception('Insufficient balance');
      }

      // Simulate transaction
      await Future.delayed(const Duration(seconds: 2));

      // Update balance
      final updatedBalances = Map<String, double>.from(state.balances);
      updatedBalances[currency] = currentBalance - amount;

      // Add transaction to history
      final transaction = wallet_models.Transaction(
        hash: '0x${DateTime.now().millisecondsSinceEpoch.toRadixString(16)}',
        type: wallet_models.TransactionType.send,
        symbol: currency,
        amount: amount.toString(),
        from: 'user_wallet', // Mock from address
        to: toAddress,
        network: 'ethereum', // Default network
        gasUsed: 21000,
        gasPrice: '20000000000', // 20 gwei
        fee: '0.00042', // Mock fee
        status: wallet_models.TransactionStatus.confirmed,
        confirmations: 12,
        timestamp: DateTime.now(),
      );

      final updatedTransactions = [transaction, ...state.transactions];

      state = state.copyWith(
        balances: updatedBalances,
        transactions: updatedTransactions,
        isLoading: false,
      );

      AppLogger.info('Transaction sent successfully');
      return true;
    } catch (e) {
      AppLogger.error('Failed to send transaction', e);
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return false;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  // Load transaction history
  Future<void> loadTransactionHistory({
    String? walletId,
    String? cryptocurrency,
    int limit = 20,
    int offset = 0,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // TODO: Replace with actual API call
      await Future.delayed(const Duration(milliseconds: 800));

      // Mock transaction history data
      final mockTransactions = _generateMockTransactionHistory(limit);

      state = state.copyWith(
        transactionHistory: offset == 0 ? mockTransactions : [...state.transactionHistory, ...mockTransactions],
        hasMoreTransactions: mockTransactions.length == limit,
        isLoading: false,
      );
    } catch (e) {
      AppLogger.error('Failed to load transaction history', e);
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  // Load more transactions (pagination)
  Future<void> loadMoreTransactions() async {
    if (state.isLoadingMore || !state.hasMoreTransactions) return;

    state = state.copyWith(isLoadingMore: true);

    try {
      // TODO: Replace with actual API call
      await Future.delayed(const Duration(milliseconds: 500));

      final moreTransactions = _generateMockTransactionHistory(10);

      state = state.copyWith(
        transactionHistory: [...state.transactionHistory, ...moreTransactions],
        hasMoreTransactions: moreTransactions.length == 10,
        isLoadingMore: false,
      );
    } catch (e) {
      AppLogger.error('Failed to load more transactions', e);
      state = state.copyWith(
        isLoadingMore: false,
        error: e.toString(),
      );
    }
  }

  // Get transaction by ID
  Future<wallet_models.WalletTransaction> getTransactionById(String transactionId) async {
    try {
      // TODO: Replace with actual API call
      await Future.delayed(const Duration(milliseconds: 300));

      // Try to find in current history first
      final existing = state.transactionHistory
          .where((t) => t.id == transactionId)
          .firstOrNull;

      if (existing != null) {
        return existing;
      }

      // Mock detailed transaction
      return wallet_models.WalletTransaction(
        id: transactionId,
        txHash: '******************************************',
        type: wallet_models.TransactionType.send,
        cryptocurrency: 'USDT',
        amount: 100.0,
        fromAddress: '******************************************',
        toAddress: '******************************************',
        network: 'Polygon',
        status: wallet_models.TransactionStatus.confirmed,
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        confirmedAt: DateTime.now().subtract(const Duration(hours: 1)),
        description: 'P2P Trade Payment',
        fiatValue: 100.0,
        fiatCurrency: 'USD',
        fee: 0.001,
        gasPrice: '20',
        gasUsed: 21000,
        blockNumber: 12345678,
        confirmations: 15,
      );
    } catch (e) {
      AppLogger.error('Failed to get transaction by ID', e);
      rethrow;
    }
  }

  // Generate mock transaction history
  List<wallet_models.WalletTransaction> _generateMockTransactionHistory(int count) {
    final transactions = <wallet_models.WalletTransaction>[];
    final types = wallet_models.TransactionType.values;
    final statuses = wallet_models.TransactionStatus.values;
    final cryptos = ['USDT', 'USDC', 'BTC', 'ETH', 'MATIC'];

    for (int i = 0; i < count; i++) {
      final type = types[i % types.length];
      final crypto = cryptos[i % cryptos.length];
      final amount = (50 + (i * 25)) % 1000 + (i * 0.123456);

      transactions.add(wallet_models.WalletTransaction(
        id: 'tx_${DateTime.now().millisecondsSinceEpoch}_$i',
        txHash: '0x${(i * 123456789).toRadixString(16).padLeft(64, '0')}',
        type: type,
        cryptocurrency: crypto,
        amount: amount,
        fromAddress: type == wallet_models.TransactionType.receive
            ? '0x${'1234567890abcdef' * 4}'
            : '0x${'abcdef1234567890' * 4}',
        toAddress: type == wallet_models.TransactionType.send
            ? '0x${'1234567890abcdef' * 4}'
            : '0x${'abcdef1234567890' * 4}',
        network: crypto == 'BTC' ? 'Bitcoin' : 'Polygon',
        status: statuses[i % statuses.length],
        createdAt: DateTime.now().subtract(Duration(hours: i * 2)),
        confirmedAt: statuses[i % statuses.length] == wallet_models.TransactionStatus.confirmed
            ? DateTime.now().subtract(Duration(hours: i * 2 - 1))
            : null,
        description: _getTransactionDescription(type, i),
        fiatValue: amount * (crypto == 'BTC' ? 45000 : crypto == 'ETH' ? 3000 : 1),
        fiatCurrency: 'USD',
        fee: crypto == 'BTC' ? 0.0001 : 0.001,
        blockNumber: 12345678 - i,
        confirmations: statuses[i % statuses.length] == wallet_models.TransactionStatus.confirmed ? 15 + i : 0,
      ));
    }

    return transactions;
  }

  String _getTransactionDescription(wallet_models.TransactionType type, int index) {
    switch (type) {
      case wallet_models.TransactionType.send:
        return 'P2P Trade Payment #${1000 + index}';
      case wallet_models.TransactionType.receive:
        return 'P2P Trade Receipt #${2000 + index}';
      case wallet_models.TransactionType.swap:
        return 'Token Swap Transaction';
      case wallet_models.TransactionType.stake:
        return 'Staking Deposit';
      case wallet_models.TransactionType.unstake:
        return 'Staking Withdrawal';
      case wallet_models.TransactionType.reward:
        return 'Staking Reward';
      case wallet_models.TransactionType.fee:
        return 'Network Transaction Fee';
      case wallet_models.TransactionType.escrowFund:
        return 'Escrow Funding';
      case wallet_models.TransactionType.escrowRelease:
        return 'Escrow Release';
    }
  }
}

// Providers
final walletProvider = StateNotifierProvider<WalletNotifier, WalletState>((ref) {
  return WalletNotifier();
});

final walletBalancesProvider = Provider<Map<String, double>>((ref) {
  final walletState = ref.watch(walletProvider);
  return walletState.balances;
});

final selectedCurrencyProvider = Provider<String?>((ref) {
  final walletState = ref.watch(walletProvider);
  return walletState.selectedCurrency;
});

final walletAddressesProvider = Provider<Map<String, String>>((ref) {
  final walletState = ref.watch(walletProvider);
  return walletState.addresses;
});

final transactionHistoryProvider = Provider<List<wallet_models.Transaction>>((ref) {
  final walletState = ref.watch(walletProvider);
  return walletState.transactions;
});

final walletTransactionHistoryProvider = Provider<List<wallet_models.WalletTransaction>>((ref) {
  final walletState = ref.watch(walletProvider);
  return walletState.transactionHistory;
});
