import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/loading_overlay.dart';
import '../../../core/widgets/error_display.dart';
import '../../../core/widgets/empty_state.dart';
import '../models/wallet_models.dart';
import '../providers/wallet_provider.dart';
import '../widgets/transaction_list_item.dart';
import '../widgets/transaction_filter_sheet.dart';

class TransactionHistoryScreen extends ConsumerStatefulWidget {
  final String? walletId;
  final String? cryptocurrency;

  const TransactionHistoryScreen({
    super.key,
    this.walletId,
    this.cryptocurrency,
  });

  @override
  ConsumerState<TransactionHistoryScreen> createState() => _TransactionHistoryScreenState();
}

class _TransactionHistoryScreenState extends ConsumerState<TransactionHistoryScreen> {
  final _searchController = TextEditingController();
  final _scrollController = ScrollController();
  
  // Filter state
  TransactionType? _selectedType;
  TransactionStatus? _selectedStatus;
  String? _selectedCryptocurrency;
  DateTimeRange? _dateRange;
  double? _minAmount;
  double? _maxAmount;
  String _sortBy = 'date';
  bool _sortDescending = true;
  
  @override
  void initState() {
    super.initState();
    _selectedCryptocurrency = widget.cryptocurrency;
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadTransactions();
    });
    
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadTransactions() async {
    await ref.read(walletProvider.notifier).loadTransactionHistory(
      walletId: widget.walletId,
      cryptocurrency: _selectedCryptocurrency,
    );
  }

  Future<void> _refreshTransactions() async {
    await _loadTransactions();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      // Load more transactions when near bottom
      ref.read(walletProvider.notifier).loadMoreTransactions();
    }
  }

  List<WalletTransaction> _getFilteredTransactions(List<WalletTransaction> transactions) {
    var filtered = transactions;
    
    // Apply filters
    if (_selectedType != null) {
      filtered = filtered.where((t) => t.type == _selectedType).toList();
    }
    
    if (_selectedStatus != null) {
      filtered = filtered.where((t) => t.status == _selectedStatus).toList();
    }
    
    if (_selectedCryptocurrency != null) {
      filtered = filtered.where((t) => t.cryptocurrency == _selectedCryptocurrency).toList();
    }
    
    if (_dateRange != null) {
      filtered = filtered.where((t) => 
        t.createdAt.isAfter(_dateRange!.start) &&
        t.createdAt.isBefore(_dateRange!.end.add(const Duration(days: 1)))
      ).toList();
    }
    
    if (_minAmount != null) {
      filtered = filtered.where((t) => t.amount >= _minAmount!).toList();
    }
    
    if (_maxAmount != null) {
      filtered = filtered.where((t) => t.amount <= _maxAmount!).toList();
    }
    
    // Apply search
    final searchTerm = _searchController.text.toLowerCase();
    if (searchTerm.isNotEmpty) {
      filtered = filtered.where((t) => 
        t.toAddress?.toLowerCase().contains(searchTerm) == true ||
        t.fromAddress?.toLowerCase().contains(searchTerm) == true ||
        t.txHash?.toLowerCase().contains(searchTerm) == true ||
        t.description?.toLowerCase().contains(searchTerm) == true
      ).toList();
    }
    
    // Apply sorting
    filtered.sort((a, b) {
      int comparison = 0;
      switch (_sortBy) {
        case 'date':
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        case 'amount':
          comparison = a.amount.compareTo(b.amount);
          break;
        case 'type':
          comparison = a.type.name.compareTo(b.type.name);
          break;
        case 'status':
          comparison = a.status.name.compareTo(b.status.name);
          break;
      }
      return _sortDescending ? -comparison : comparison;
    });
    
    return filtered;
  }

  void _showFilterSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => TransactionFilterSheet(
        selectedType: _selectedType,
        selectedStatus: _selectedStatus,
        selectedCryptocurrency: _selectedCryptocurrency,
        dateRange: _dateRange,
        minAmount: _minAmount,
        maxAmount: _maxAmount,
        sortBy: _sortBy,
        sortDescending: _sortDescending,
        onApplyFilters: (filters) {
          setState(() {
            _selectedType = filters['type'];
            _selectedStatus = filters['status'];
            _selectedCryptocurrency = filters['cryptocurrency'];
            _dateRange = filters['dateRange'];
            _minAmount = filters['minAmount'];
            _maxAmount = filters['maxAmount'];
            _sortBy = filters['sortBy'] ?? 'date';
            _sortDescending = filters['sortDescending'] ?? true;
          });
        },
        onClearFilters: () {
          setState(() {
            _selectedType = null;
            _selectedStatus = null;
            _selectedCryptocurrency = widget.cryptocurrency;
            _dateRange = null;
            _minAmount = null;
            _maxAmount = null;
            _sortBy = 'date';
            _sortDescending = true;
          });
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final walletState = ref.watch(walletProvider);
    final filteredTransactions = _getFilteredTransactions(walletState.transactions);
    
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Transaction History',
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterSheet,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportTransactions,
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Container(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search transactions...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {});
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onChanged: (value) => setState(() {}),
            ),
          ),
          
          // Filter chips
          if (_hasActiveFilters())
            Container(
              height: 50,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: ListView(
                scrollDirection: Axis.horizontal,
                children: _buildFilterChips(),
              ),
            ),
          
          // Summary stats
          if (filteredTransactions.isNotEmpty)
            _buildSummaryStats(filteredTransactions),
          
          // Transactions list
          Expanded(
            child: _buildTransactionsList(walletState, filteredTransactions),
          ),
        ],
      ),
    );
  }

  bool _hasActiveFilters() {
    return _selectedType != null ||
           _selectedStatus != null ||
           _selectedCryptocurrency != null ||
           _dateRange != null ||
           _minAmount != null ||
           _maxAmount != null ||
           _sortBy != 'date' ||
           !_sortDescending;
  }

  List<Widget> _buildFilterChips() {
    final chips = <Widget>[];
    
    if (_selectedType != null) {
      chips.add(_buildFilterChip(
        _selectedType!.displayName,
        () => setState(() => _selectedType = null),
      ));
    }
    
    if (_selectedStatus != null) {
      chips.add(_buildFilterChip(
        _selectedStatus!.displayName,
        () => setState(() => _selectedStatus = null),
      ));
    }
    
    if (_selectedCryptocurrency != null && _selectedCryptocurrency != widget.cryptocurrency) {
      chips.add(_buildFilterChip(
        _selectedCryptocurrency!,
        () => setState(() => _selectedCryptocurrency = widget.cryptocurrency),
      ));
    }
    
    if (_dateRange != null) {
      chips.add(_buildFilterChip(
        '${DateFormat('MMM d').format(_dateRange!.start)} - ${DateFormat('MMM d').format(_dateRange!.end)}',
        () => setState(() => _dateRange = null),
      ));
    }
    
    if (_minAmount != null || _maxAmount != null) {
      final amountText = _minAmount != null && _maxAmount != null
          ? '${_minAmount!.toStringAsFixed(2)}-${_maxAmount!.toStringAsFixed(2)}'
          : _minAmount != null
              ? '>${_minAmount!.toStringAsFixed(2)}'
              : '<${_maxAmount!.toStringAsFixed(2)}';
      chips.add(_buildFilterChip(
        amountText,
        () => setState(() {
          _minAmount = null;
          _maxAmount = null;
        }),
      ));
    }
    
    return chips;
  }

  Widget _buildFilterChip(String label, VoidCallback onDeleted) {
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: Chip(
        label: Text(label),
        onDeleted: onDeleted,
        deleteIcon: const Icon(Icons.close, size: 18),
        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
        labelStyle: TextStyle(
          color: Theme.of(context).colorScheme.onPrimaryContainer,
        ),
      ),
    );
  }

  Widget _buildSummaryStats(List<WalletTransaction> transactions) {
    final theme = Theme.of(context);
    
    final totalIn = transactions
        .where((t) => t.type == TransactionType.receive)
        .fold(0.0, (sum, t) => sum + t.amount);
    
    final totalOut = transactions
        .where((t) => t.type == TransactionType.send)
        .fold(0.0, (sum, t) => sum + t.amount);
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              children: [
                Text(
                  'Total In',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '+${totalIn.toStringAsFixed(6)}',
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: theme.colorScheme.outline.withOpacity(0.3),
          ),
          Expanded(
            child: Column(
              children: [
                Text(
                  'Total Out',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '-${totalOut.toStringAsFixed(6)}',
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: theme.colorScheme.outline.withOpacity(0.3),
          ),
          Expanded(
            child: Column(
              children: [
                Text(
                  'Net',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${(totalIn - totalOut).toStringAsFixed(6)}',
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: totalIn >= totalOut ? Colors.green : Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsList(WalletState state, List<WalletTransaction> transactions) {
    if (state.isLoading && transactions.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }
    
    if (state.hasError && transactions.isEmpty) {
      return ErrorDisplay(
        error: state.error!,
        onRetry: _loadTransactions,
      );
    }
    
    if (transactions.isEmpty) {
      return EmptyState(
        icon: Icons.receipt_long_outlined,
        title: _hasActiveFilters() ? 'No matching transactions' : 'No transactions yet',
        subtitle: _hasActiveFilters()
            ? 'Try adjusting your filters'
            : 'Your transaction history will appear here',
        actionText: _hasActiveFilters() ? 'Clear Filters' : null,
        onAction: _hasActiveFilters() ? () {
          setState(() {
            _selectedType = null;
            _selectedStatus = null;
            _selectedCryptocurrency = widget.cryptocurrency;
            _dateRange = null;
            _minAmount = null;
            _maxAmount = null;
            _sortBy = 'date';
            _sortDescending = true;
            _searchController.clear();
          });
        } : null,
      );
    }
    
    return RefreshIndicator(
      onRefresh: _refreshTransactions,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        itemCount: transactions.length + (state.isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == transactions.length) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: CircularProgressIndicator(),
              ),
            );
          }
          
          final transaction = transactions[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: TransactionListItem(
              transaction: transaction,
              onTap: () => _openTransactionDetail(transaction),
            ),
          );
        },
      ),
    );
  }

  void _openTransactionDetail(WalletTransaction transaction) {
    context.push('/wallet/transaction/${transaction.id}');
  }

  void _exportTransactions() {
    // TODO: Implement transaction export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Export functionality coming soon'),
      ),
    );
  }
}
