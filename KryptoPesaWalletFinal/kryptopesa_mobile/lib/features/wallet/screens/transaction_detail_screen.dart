import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/loading_overlay.dart';
import '../../../core/widgets/error_display.dart';
import '../models/wallet_models.dart';
import '../providers/wallet_provider.dart';
import '../widgets/transaction_status_indicator.dart';

class TransactionDetailScreen extends ConsumerStatefulWidget {
  final String transactionId;

  const TransactionDetailScreen({
    super.key,
    required this.transactionId,
  });

  @override
  ConsumerState<TransactionDetailScreen> createState() => _TransactionDetailScreenState();
}

class _TransactionDetailScreenState extends ConsumerState<TransactionDetailScreen> {
  WalletTransaction? _transaction;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadTransactionDetails();
  }

  Future<void> _loadTransactionDetails() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final transaction = await ref.read(walletProvider.notifier)
          .getTransactionById(widget.transactionId);
      
      if (mounted) {
        setState(() {
          _transaction = transaction;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: CustomAppBar(title: 'Transaction Details'),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_error != null) {
      return Scaffold(
        appBar: CustomAppBar(title: 'Transaction Details'),
        body: ErrorDisplay(
          error: _error!,
          onRetry: _loadTransactionDetails,
        ),
      );
    }

    if (_transaction == null) {
      return Scaffold(
        appBar: CustomAppBar(title: 'Transaction Details'),
        body: const Center(
          child: Text('Transaction not found'),
        ),
      );
    }

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Transaction Details',
        actions: [
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'copy_hash',
                child: Text('Copy Transaction Hash'),
              ),
              const PopupMenuItem(
                value: 'view_explorer',
                child: Text('View on Explorer'),
              ),
              const PopupMenuItem(
                value: 'share',
                child: Text('Share'),
              ),
            ],
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadTransactionDetails,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Status card
              _buildStatusCard(),
              const SizedBox(height: 16),

              // Amount card
              _buildAmountCard(),
              const SizedBox(height: 16),

              // Details card
              _buildDetailsCard(),
              const SizedBox(height: 16),

              // Addresses card
              _buildAddressesCard(),
              const SizedBox(height: 16),

              // Blockchain info card
              _buildBlockchainInfoCard(),
              const SizedBox(height: 16),

              // Timeline card
              if (_transaction!.hasTimeline)
                _buildTimelineCard(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                TransactionStatusIndicator(status: _transaction!.status),
                const Spacer(),
                Text(
                  DateFormat('MMM d, yyyy • HH:mm').format(_transaction!.createdAt),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              _getStatusDescription(),
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAmountCard() {
    final theme = Theme.of(context);
    final isReceive = _transaction!.type == TransactionType.receive;
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(
              isReceive ? Icons.call_received : Icons.call_made,
              size: 48,
              color: isReceive ? Colors.green : Colors.red,
            ),
            const SizedBox(height: 16),
            
            Text(
              '${isReceive ? '+' : '-'}${_transaction!.amount.toStringAsFixed(8)}',
              style: theme.textTheme.headlineMedium?.copyWith(
                color: isReceive ? Colors.green : Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              _transaction!.cryptocurrency,
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            
            if (_transaction!.fiatValue != null) ...[
              const SizedBox(height: 8),
              Text(
                '≈ \$${_transaction!.fiatValue!.toStringAsFixed(2)} USD',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailsCard() {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Transaction Details',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            _buildDetailRow('Type', _transaction!.type.displayName),
            _buildDetailRow('Network', _transaction!.network),
            
            if (_transaction!.fee != null)
              _buildDetailRow(
                'Network Fee',
                '${_transaction!.fee!.toStringAsFixed(8)} ${_transaction!.cryptocurrency}',
              ),
            
            if (_transaction!.gasPrice != null)
              _buildDetailRow('Gas Price', '${_transaction!.gasPrice} Gwei'),
            
            if (_transaction!.gasUsed != null)
              _buildDetailRow('Gas Used', _transaction!.gasUsed.toString()),
            
            if (_transaction!.nonce != null)
              _buildDetailRow('Nonce', _transaction!.nonce.toString()),
            
            if (_transaction!.description != null)
              _buildDetailRow('Description', _transaction!.description!),
          ],
        ),
      ),
    );
  }

  Widget _buildAddressesCard() {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Addresses',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            if (_transaction!.fromAddress != null)
              _buildAddressRow('From', _transaction!.fromAddress!),
            
            if (_transaction!.toAddress != null)
              _buildAddressRow('To', _transaction!.toAddress!),
          ],
        ),
      ),
    );
  }

  Widget _buildBlockchainInfoCard() {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Blockchain Information',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            if (_transaction!.txHash != null)
              _buildDetailRow('Transaction Hash', _transaction!.txHash!, copyable: true),
            
            if (_transaction!.blockNumber != null)
              _buildDetailRow('Block Number', _transaction!.blockNumber.toString()),
            
            if (_transaction!.blockHash != null)
              _buildDetailRow('Block Hash', _transaction!.blockHash!, copyable: true),
            
            if (_transaction!.confirmations != null)
              _buildDetailRow('Confirmations', _transaction!.confirmations.toString()),
          ],
        ),
      ),
    );
  }

  Widget _buildTimelineCard() {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Timeline',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // TODO: Implement timeline widget
            Text(
              'Timeline functionality coming soon',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {bool copyable = false}) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          Expanded(
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    value,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                if (copyable)
                  IconButton(
                    icon: const Icon(Icons.copy, size: 16),
                    onPressed: () => _copyToClipboard(value),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressRow(String label, String address) {
    final theme = Theme.of(context);
    final shortAddress = '${address.substring(0, 6)}...${address.substring(address.length - 4)}';
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          SizedBox(
            width: 60,
            child: Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceVariant.withOpacity(0.5),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      shortAddress,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontFamily: 'monospace',
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.copy, size: 16),
                    onPressed: () => _copyToClipboard(address),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getStatusDescription() {
    switch (_transaction!.status) {
      case TransactionStatus.pending:
        return 'Transaction is being processed on the network';
      case TransactionStatus.confirmed:
        return 'Transaction has been confirmed on the blockchain';
      case TransactionStatus.failed:
        return 'Transaction failed to complete';
      case TransactionStatus.cancelled:
        return 'Transaction was cancelled';
      default:
        return 'Unknown status';
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'copy_hash':
        if (_transaction!.txHash != null) {
          _copyToClipboard(_transaction!.txHash!);
        }
        break;
      case 'view_explorer':
        _openInExplorer();
        break;
      case 'share':
        _shareTransaction();
        break;
    }
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Copied to clipboard'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _openInExplorer() async {
    if (_transaction!.txHash == null) return;
    
    // TODO: Get explorer URL based on network
    final explorerUrl = _getExplorerUrl(_transaction!.network, _transaction!.txHash!);
    
    if (await canLaunchUrl(Uri.parse(explorerUrl))) {
      await launchUrl(Uri.parse(explorerUrl));
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not open blockchain explorer'),
          ),
        );
      }
    }
  }

  String _getExplorerUrl(String network, String txHash) {
    switch (network.toLowerCase()) {
      case 'ethereum':
        return 'https://etherscan.io/tx/$txHash';
      case 'polygon':
        return 'https://polygonscan.com/tx/$txHash';
      case 'bsc':
        return 'https://bscscan.com/tx/$txHash';
      default:
        return 'https://etherscan.io/tx/$txHash';
    }
  }

  void _shareTransaction() {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share functionality coming soon'),
      ),
    );
  }
}
