import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';
import '../models/wallet_models.dart';

class TransactionFilterSheet extends StatefulWidget {
  final TransactionType? selectedType;
  final TransactionStatus? selectedStatus;
  final String? selectedCryptocurrency;
  final DateTimeRange? dateRange;
  final double? minAmount;
  final double? maxAmount;
  final String sortBy;
  final bool sortDescending;
  final Function(Map<String, dynamic>) onApplyFilters;
  final VoidCallback onClearFilters;

  const TransactionFilterSheet({
    super.key,
    this.selectedType,
    this.selectedStatus,
    this.selectedCryptocurrency,
    this.dateRange,
    this.minAmount,
    this.maxAmount,
    required this.sortBy,
    required this.sortDescending,
    required this.onApplyFilters,
    required this.onClearFilters,
  });

  @override
  State<TransactionFilterSheet> createState() => _TransactionFilterSheetState();
}

class _TransactionFilterSheetState extends State<TransactionFilterSheet> {
  late TransactionType? _type;
  late TransactionStatus? _status;
  late String? _cryptocurrency;
  late DateTimeRange? _dateRange;
  late double? _minAmount;
  late double? _maxAmount;
  late String _sortBy;
  late bool _sortDescending;

  final _minAmountController = TextEditingController();
  final _maxAmountController = TextEditingController();

  // Available options
  final List<String> _cryptocurrencies = ['All', 'USDT', 'USDC', 'BTC', 'ETH', 'MATIC'];
  final List<String> _sortOptions = ['date', 'amount', 'type', 'status'];

  @override
  void initState() {
    super.initState();
    _type = widget.selectedType;
    _status = widget.selectedStatus;
    _cryptocurrency = widget.selectedCryptocurrency;
    _dateRange = widget.dateRange;
    _minAmount = widget.minAmount;
    _maxAmount = widget.maxAmount;
    _sortBy = widget.sortBy;
    _sortDescending = widget.sortDescending;

    _minAmountController.text = _minAmount?.toString() ?? '';
    _maxAmountController.text = _maxAmount?.toString() ?? '';
  }

  @override
  void dispose() {
    _minAmountController.dispose();
    _maxAmountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: colorScheme.onSurfaceVariant.withOpacity(0.4),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Text(
                  'Filter Transactions',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _clearFilters,
                  child: Text('Clear All'),
                ),
              ],
            ),
          ),

          // Filters content
          Flexible(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Transaction Type
                  _buildSectionTitle('Transaction Type'),
                  _buildTypeSelector(),
                  const SizedBox(height: 24),

                  // Status
                  _buildSectionTitle('Status'),
                  _buildStatusSelector(),
                  const SizedBox(height: 24),

                  // Cryptocurrency
                  _buildSectionTitle('Cryptocurrency'),
                  _buildCryptocurrencySelector(),
                  const SizedBox(height: 24),

                  // Date Range
                  _buildSectionTitle('Date Range'),
                  _buildDateRangeSelector(),
                  const SizedBox(height: 24),

                  // Amount Range
                  _buildSectionTitle('Amount Range'),
                  _buildAmountRangeInputs(),
                  const SizedBox(height: 24),

                  // Sort Options
                  _buildSectionTitle('Sort By'),
                  _buildSortOptions(),
                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),

          // Action buttons
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(
                  color: colorScheme.outline.withOpacity(0.2),
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _applyFilters,
                    child: Text('Apply Filters'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildTypeSelector() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        _buildChoiceChip(
          label: 'All',
          selected: _type == null,
          onSelected: (selected) {
            if (selected) setState(() => _type = null);
          },
        ),
        ...TransactionType.values.map((type) => _buildChoiceChip(
          label: type.displayName,
          selected: _type == type,
          onSelected: (selected) {
            setState(() => _type = selected ? type : null);
          },
        )),
      ],
    );
  }

  Widget _buildStatusSelector() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        _buildChoiceChip(
          label: 'All',
          selected: _status == null,
          onSelected: (selected) {
            if (selected) setState(() => _status = null);
          },
        ),
        ...TransactionStatus.values.map((status) => _buildChoiceChip(
          label: status.displayName,
          selected: _status == status,
          onSelected: (selected) {
            setState(() => _status = selected ? status : null);
          },
        )),
      ],
    );
  }

  Widget _buildCryptocurrencySelector() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: _cryptocurrencies.map((crypto) => _buildChoiceChip(
        label: crypto,
        selected: crypto == 'All' ? _cryptocurrency == null : _cryptocurrency == crypto,
        onSelected: (selected) {
          setState(() => _cryptocurrency = selected ? (crypto == 'All' ? null : crypto) : null);
        },
      )).toList(),
    );
  }

  Widget _buildDateRangeSelector() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _selectDateRange,
            icon: const Icon(Icons.date_range),
            label: Text(
              _dateRange != null
                  ? '${_formatDate(_dateRange!.start)} - ${_formatDate(_dateRange!.end)}'
                  : 'Select Date Range',
            ),
          ),
        ),
        if (_dateRange != null) ...[
          const SizedBox(height: 8),
          SizedBox(
            width: double.infinity,
            child: TextButton(
              onPressed: () => setState(() => _dateRange = null),
              child: Text('Clear Date Range'),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildAmountRangeInputs() {
    return Row(
      children: [
        Expanded(
          child: TextField(
            controller: _minAmountController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              labelText: 'Min Amount',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onChanged: (value) {
              _minAmount = double.tryParse(value);
            },
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: TextField(
            controller: _maxAmountController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              labelText: 'Max Amount',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onChanged: (value) {
              _maxAmount = double.tryParse(value);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSortOptions() {
    return Column(
      children: [
        DropdownButtonFormField<String>(
          value: _sortBy,
          decoration: InputDecoration(
            labelText: 'Sort by',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          items: _sortOptions.map((option) {
            return DropdownMenuItem(
              value: option,
              child: Text(_getSortDisplayName(option)),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() => _sortBy = value);
            }
          },
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildChoiceChip(
                label: 'Ascending',
                selected: !_sortDescending,
                onSelected: (selected) {
                  if (selected) setState(() => _sortDescending = false);
                },
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildChoiceChip(
                label: 'Descending',
                selected: _sortDescending,
                onSelected: (selected) {
                  if (selected) setState(() => _sortDescending = true);
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildChoiceChip({
    required String label,
    required bool selected,
    required Function(bool) onSelected,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return FilterChip(
      label: Text(label),
      selected: selected,
      onSelected: onSelected,
      backgroundColor: colorScheme.surface,
      selectedColor: colorScheme.primaryContainer,
      checkmarkColor: colorScheme.onPrimaryContainer,
      labelStyle: TextStyle(
        color: selected 
            ? colorScheme.onPrimaryContainer 
            : colorScheme.onSurface,
      ),
      side: BorderSide(
        color: selected 
            ? colorScheme.primary 
            : colorScheme.outline,
      ),
    );
  }

  String _getSortDisplayName(String sortBy) {
    switch (sortBy) {
      case 'date':
        return 'Date';
      case 'amount':
        return 'Amount';
      case 'type':
        return 'Type';
      case 'status':
        return 'Status';
      default:
        return sortBy;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: _dateRange,
    );
    
    if (picked != null) {
      setState(() => _dateRange = picked);
    }
  }

  void _clearFilters() {
    setState(() {
      _type = null;
      _status = null;
      _cryptocurrency = null;
      _dateRange = null;
      _minAmount = null;
      _maxAmount = null;
      _sortBy = 'date';
      _sortDescending = true;
      _minAmountController.clear();
      _maxAmountController.clear();
    });
    widget.onClearFilters();
  }

  void _applyFilters() {
    widget.onApplyFilters({
      'type': _type,
      'status': _status,
      'cryptocurrency': _cryptocurrency,
      'dateRange': _dateRange,
      'minAmount': _minAmount,
      'maxAmount': _maxAmount,
      'sortBy': _sortBy,
      'sortDescending': _sortDescending,
    });
    Navigator.pop(context);
  }
}
