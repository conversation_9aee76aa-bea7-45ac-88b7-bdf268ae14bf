import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/wallet_models.dart';
import 'transaction_status_indicator.dart';

class TransactionListItem extends StatelessWidget {
  final WalletTransaction transaction;
  final VoidCallback? onTap;

  const TransactionListItem({
    super.key,
    required this.transaction,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isReceive = transaction.type == TransactionType.receive;
    
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Transaction type icon
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: _getTypeColor(transaction.type).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  _getTypeIcon(transaction.type),
                  color: _getTypeColor(transaction.type),
                  size: 24,
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Transaction details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Type and status row
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            _getTransactionTitle(),
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        TransactionStatusIndicator(
                          status: transaction.status,
                          showLabel: false,
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 4),
                    
                    // Address or description
                    Text(
                      _getSubtitle(),
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Amount and timestamp row
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            '${isReceive ? '+' : '-'}${transaction.amount.toStringAsFixed(6)} ${transaction.cryptocurrency}',
                            style: theme.textTheme.titleSmall?.copyWith(
                              color: isReceive ? Colors.green : Colors.red,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Text(
                          _formatTimestamp(transaction.createdAt),
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                    
                    // Fiat value (if available)
                    if (transaction.fiatValue != null) ...[
                      const SizedBox(height: 2),
                      Text(
                        '≈ \$${transaction.fiatValue!.toStringAsFixed(2)} USD',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getTypeIcon(TransactionType type) {
    switch (type) {
      case TransactionType.send:
        return Icons.call_made;
      case TransactionType.receive:
        return Icons.call_received;
      case TransactionType.swap:
        return Icons.swap_horiz;
      case TransactionType.stake:
        return Icons.savings;
      case TransactionType.unstake:
        return Icons.account_balance_wallet;
      case TransactionType.reward:
        return Icons.card_giftcard;
      case TransactionType.fee:
        return Icons.receipt;
      default:
        return Icons.compare_arrows;
    }
  }

  Color _getTypeColor(TransactionType type) {
    switch (type) {
      case TransactionType.send:
        return Colors.red;
      case TransactionType.receive:
        return Colors.green;
      case TransactionType.swap:
        return Colors.blue;
      case TransactionType.stake:
        return Colors.purple;
      case TransactionType.unstake:
        return Colors.orange;
      case TransactionType.reward:
        return Colors.amber;
      case TransactionType.fee:
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }

  String _getTransactionTitle() {
    switch (transaction.type) {
      case TransactionType.send:
        return 'Sent';
      case TransactionType.receive:
        return 'Received';
      case TransactionType.swap:
        return 'Swapped';
      case TransactionType.stake:
        return 'Staked';
      case TransactionType.unstake:
        return 'Unstaked';
      case TransactionType.reward:
        return 'Reward';
      case TransactionType.fee:
        return 'Network Fee';
      case TransactionType.escrowFund:
        return 'Escrow Fund';
      case TransactionType.escrowRelease:
        return 'Escrow Release';
      default:
        return 'Transaction';
    }
  }

  String _getSubtitle() {
    if (transaction.description != null && transaction.description!.isNotEmpty) {
      return transaction.description!;
    }
    
    switch (transaction.type) {
      case TransactionType.send:
        return transaction.toAddress != null 
            ? 'To: ${_formatAddress(transaction.toAddress!)}'
            : 'Outgoing transaction';
      case TransactionType.receive:
        return transaction.fromAddress != null 
            ? 'From: ${_formatAddress(transaction.fromAddress!)}'
            : 'Incoming transaction';
      case TransactionType.swap:
        return 'Token swap transaction';
      case TransactionType.stake:
        return 'Staking transaction';
      case TransactionType.unstake:
        return 'Unstaking transaction';
      case TransactionType.reward:
        return 'Staking reward';
      case TransactionType.fee:
        return 'Network transaction fee';
      case TransactionType.escrowFund:
        return 'Escrow funding';
      case TransactionType.escrowRelease:
        return 'Escrow release';
      default:
        return 'Blockchain transaction';
    }
  }

  String _formatAddress(String address) {
    if (address.length <= 12) return address;
    return '${address.substring(0, 6)}...${address.substring(address.length - 4)}';
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return DateFormat('MMM d').format(timestamp);
    }
  }
}
