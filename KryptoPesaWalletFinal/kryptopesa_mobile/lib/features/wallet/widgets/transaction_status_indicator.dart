import 'package:flutter/material.dart';
import '../models/wallet_models.dart';

class TransactionStatusIndicator extends StatelessWidget {
  final TransactionStatus status;
  final bool showLabel;

  const TransactionStatusIndicator({
    super.key,
    required this.status,
    this.showLabel = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final statusInfo = _getStatusInfo(status);
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: statusInfo.color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: statusInfo.color.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                statusInfo.icon,
                size: 12,
                color: statusInfo.color,
              ),
              if (showLabel) ...[
                const SizedBox(width: 4),
                Text(
                  statusInfo.label,
                  style: theme.textTheme.labelSmall?.copyWith(
                    color: statusInfo.color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  TransactionStatusInfo _getStatusInfo(TransactionStatus status) {
    switch (status) {
      case TransactionStatus.pending:
        return TransactionStatusInfo(
          label: 'Pending',
          icon: Icons.schedule,
          color: Colors.orange,
        );
      case TransactionStatus.confirmed:
        return TransactionStatusInfo(
          label: 'Confirmed',
          icon: Icons.check_circle,
          color: Colors.green,
        );
      case TransactionStatus.failed:
        return TransactionStatusInfo(
          label: 'Failed',
          icon: Icons.error,
          color: Colors.red,
        );
      case TransactionStatus.cancelled:
        return TransactionStatusInfo(
          label: 'Cancelled',
          icon: Icons.cancel,
          color: Colors.grey,
        );
      default:
        return TransactionStatusInfo(
          label: 'Unknown',
          icon: Icons.help,
          color: Colors.grey,
        );
    }
  }
}

class TransactionStatusInfo {
  final String label;
  final IconData icon;
  final Color color;

  TransactionStatusInfo({
    required this.label,
    required this.icon,
    required this.color,
  });
}
