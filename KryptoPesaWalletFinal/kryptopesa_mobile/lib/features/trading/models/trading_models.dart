import 'package:json_annotation/json_annotation.dart';

part 'trading_models.g.dart';

// Type aliases for backward compatibility
typedef Offer = TradingOffer;

// Trading Offer Model
@JsonSerializable()
class TradingOffer {
  final String id;
  final String userId;
  final String username;
  final double userRating;
  final int completedTrades;
  final OfferType type;
  final String cryptocurrency;
  final String fiatCurrency;
  final double amount;
  final double minAmount;
  final double maxAmount;
  final double price;
  final double margin;
  final List<String> paymentMethods;
  final String? terms;
  final OfferStatus status;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? expiresAt;
  final String? location;
  final bool isOnline;

  const TradingOffer({
    required this.id,
    required this.userId,
    required this.username,
    required this.userRating,
    required this.completedTrades,
    required this.type,
    required this.cryptocurrency,
    required this.fiatCurrency,
    required this.amount,
    required this.minAmount,
    required this.maxAmount,
    required this.price,
    required this.margin,
    required this.paymentMethods,
    this.terms,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.expiresAt,
    this.location,
    required this.isOnline,
  });

  factory TradingOffer.fromJson(Map<String, dynamic> json) => _$TradingOfferFromJson(json);
  Map<String, dynamic> toJson() => _$TradingOfferToJson(this);

  bool get isBuyOffer => type == OfferType.buy;
  bool get isSellOffer => type == OfferType.sell;
  bool get isActive => status == OfferStatus.active;
  bool get isExpired => expiresAt != null && DateTime.now().isAfter(expiresAt!);
  
  String get displayPrice => '${price.toStringAsFixed(2)} $fiatCurrency';
  String get displayAmount => '${amount.toStringAsFixed(8)} $cryptocurrency';
  String get displayRange => '${minAmount.toStringAsFixed(2)} - ${maxAmount.toStringAsFixed(2)} $fiatCurrency';
}

enum OfferType {
  @JsonValue('buy')
  buy,
  @JsonValue('sell')
  sell,
}

enum OfferStatus {
  @JsonValue('active')
  active,
  @JsonValue('paused')
  paused,
  @JsonValue('completed')
  completed,
  @JsonValue('cancelled')
  cancelled,
  @JsonValue('expired')
  expired,
}

// Trade Model
@JsonSerializable()
class Trade {
  final String id;
  final String offerId;
  final TradingOffer offer;
  final String buyerId;
  final String sellerId;
  final TradeUser buyer;
  final TradeUser seller;
  final double amount;
  final double cryptoAmount;
  final double price;
  final String cryptocurrency;
  final String fiatCurrency;
  final String paymentMethod;
  final TradeStatus status;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? acceptedAt;
  final DateTime? escrowFundedAt;
  final DateTime? paymentSentAt;
  final DateTime? paymentConfirmedAt;
  final DateTime? paidAt;
  final DateTime? completedAt;
  final DateTime? cancelledAt;
  final DateTime? disputedAt;
  final DateTime? disputeCreatedAt;
  final DateTime? expiresAt;
  final String? paymentDetails;
  final String? paymentReference;
  final String? cancellationReason;
  final TradeEscrow? escrow;
  final TradeDispute? dispute;
  final List<TradeMessage> messages;
  final List<TradeProof> proofs;
  final String? cancelReason;

  const Trade({
    required this.id,
    required this.offerId,
    required this.offer,
    required this.buyerId,
    required this.sellerId,
    required this.buyer,
    required this.seller,
    required this.amount,
    required this.cryptoAmount,
    required this.price,
    required this.cryptocurrency,
    required this.fiatCurrency,
    required this.paymentMethod,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.acceptedAt,
    this.escrowFundedAt,
    this.paymentSentAt,
    this.paymentConfirmedAt,
    this.paidAt,
    this.completedAt,
    this.cancelledAt,
    this.disputedAt,
    this.disputeCreatedAt,
    this.expiresAt,
    this.paymentDetails,
    this.paymentReference,
    this.cancellationReason,
    this.escrow,
    this.dispute,
    required this.messages,
    required this.proofs,
    this.cancelReason,
  });

  factory Trade.fromJson(Map<String, dynamic> json) => _$TradeFromJson(json);
  Map<String, dynamic> toJson() => _$TradeToJson(this);

  bool get isPending => status == TradeStatus.pending;
  bool get isAccepted => status == TradeStatus.accepted;
  bool get isFunded => status == TradeStatus.funded;
  bool get isEscrowed => status == TradeStatus.escrowed;
  bool get isPaymentSent => status == TradeStatus.paymentSent;
  bool get isPaid => status == TradeStatus.paid;
  bool get isCompleted => status == TradeStatus.completed;
  bool get isCancelled => status == TradeStatus.cancelled;
  bool get isDisputed => status == TradeStatus.disputed;
  bool get isExpired => status == TradeStatus.expired;

  bool get hasActiveEscrow => escrow != null && escrow!.status == EscrowStatus.active;
  bool get hasDispute => dispute != null && dispute!.status == DisputeStatus.open;

  // User role helpers
  bool get isUserBuyer => false; // TODO: Implement based on current user
  bool get isUserSeller => false; // TODO: Implement based on current user
  bool get isBuyTrade => false; // TODO: Implement based on offer type

  // Counterparty info helpers
  String get counterpartyUsername => isUserBuyer ? seller.username : buyer.username;
  String get counterpartyId => isUserBuyer ? sellerId : buyerId;
  double get counterpartyRating => isUserBuyer ? seller.rating : buyer.rating;
  int get counterpartyTrades => isUserBuyer ? seller.completedTrades : buyer.completedTrades;
  bool get counterpartyOnline => isUserBuyer ? seller.isOnline : buyer.isOnline;

  String get displayAmount => '${amount.toStringAsFixed(2)} $fiatCurrency';
  String get displayCryptoAmount => '${cryptoAmount.toStringAsFixed(8)} $cryptocurrency';
  String get displayPrice => '${price.toStringAsFixed(2)} $fiatCurrency';
}

enum TradeStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('accepted')
  accepted,
  @JsonValue('funded')
  funded,
  @JsonValue('escrowed')
  escrowed,
  @JsonValue('paymentSent')
  paymentSent,
  @JsonValue('paid')
  paid,
  @JsonValue('completed')
  completed,
  @JsonValue('cancelled')
  cancelled,
  @JsonValue('disputed')
  disputed,
  @JsonValue('expired')
  expired,
}

// Trade User
@JsonSerializable()
class TradeUser {
  final String id;
  final String username;
  final String? firstName;
  final String? lastName;
  final double rating;
  final int completedTrades;
  final DateTime lastSeen;
  final bool isOnline;
  final bool isVerified;

  const TradeUser({
    required this.id,
    required this.username,
    this.firstName,
    this.lastName,
    required this.rating,
    required this.completedTrades,
    required this.lastSeen,
    required this.isOnline,
    required this.isVerified,
  });

  factory TradeUser.fromJson(Map<String, dynamic> json) => _$TradeUserFromJson(json);
  Map<String, dynamic> toJson() => _$TradeUserToJson(this);

  String get displayName => firstName != null && lastName != null 
      ? '$firstName $lastName' 
      : username;
}

// Trade Escrow
@JsonSerializable()
class TradeEscrow {
  final String escrowId;
  final String? contractAddress;
  final String txHash;
  final String amount;
  final String cryptocurrency;
  final String network;
  final EscrowStatus status;
  final DateTime createdAt;
  final DateTime? releasedAt;
  final String? releasedBy;
  final String? releaseTxHash;

  const TradeEscrow({
    required this.escrowId,
    this.contractAddress,
    required this.txHash,
    required this.amount,
    required this.cryptocurrency,
    required this.network,
    required this.status,
    required this.createdAt,
    this.releasedAt,
    this.releasedBy,
    this.releaseTxHash,
  });

  factory TradeEscrow.fromJson(Map<String, dynamic> json) => _$TradeEscrowFromJson(json);
  Map<String, dynamic> toJson() => _$TradeEscrowToJson(this);

  bool get isActive => status == EscrowStatus.active;
  bool get isReleased => status == EscrowStatus.released;
  bool get isCancelled => status == EscrowStatus.cancelled;
}

enum EscrowStatus {
  @JsonValue('active')
  active,
  @JsonValue('released')
  released,
  @JsonValue('cancelled')
  cancelled,
  @JsonValue('disputed')
  disputed,
}

// Trade Dispute
@JsonSerializable()
class TradeDispute {
  final String initiatedBy;
  final String reason;
  final DisputeStatus status;
  final DateTime initiatedAt;
  final DateTime? resolvedAt;
  final String? resolvedBy;
  final String? resolution;

  const TradeDispute({
    required this.initiatedBy,
    required this.reason,
    required this.status,
    required this.initiatedAt,
    this.resolvedAt,
    this.resolvedBy,
    this.resolution,
  });

  factory TradeDispute.fromJson(Map<String, dynamic> json) => _$TradeDisputeFromJson(json);
  Map<String, dynamic> toJson() => _$TradeDisputeToJson(this);

  bool get isOpen => status == DisputeStatus.open;
  bool get isResolved => status == DisputeStatus.resolved;
}

enum DisputeStatus {
  @JsonValue('open')
  open,
  @JsonValue('resolved')
  resolved,
}

// Trade Message
@JsonSerializable()
class TradeMessage {
  final String id;
  final String senderId;
  final String senderUsername;
  final String message;
  final MessageType type;
  final DateTime timestamp;
  final bool isRead;
  final String? attachmentUrl;

  const TradeMessage({
    required this.id,
    required this.senderId,
    required this.senderUsername,
    required this.message,
    required this.type,
    required this.timestamp,
    required this.isRead,
    this.attachmentUrl,
  });

  factory TradeMessage.fromJson(Map<String, dynamic> json) => _$TradeMessageFromJson(json);
  Map<String, dynamic> toJson() => _$TradeMessageToJson(this);

  bool get isSystemMessage => type == MessageType.system;
  bool get hasAttachment => attachmentUrl != null;
}

enum MessageType {
  @JsonValue('user')
  user,
  @JsonValue('system')
  system,
  @JsonValue('payment_proof')
  paymentProof,
}

// Trade Proof
@JsonSerializable()
class TradeProof {
  final String id;
  final String uploadedBy;
  final ProofType type;
  final String description;
  final String fileUrl;
  final String? fileName;
  final int? fileSize;
  final DateTime uploadedAt;
  final bool isVerified;

  const TradeProof({
    required this.id,
    required this.uploadedBy,
    required this.type,
    required this.description,
    required this.fileUrl,
    this.fileName,
    this.fileSize,
    required this.uploadedAt,
    required this.isVerified,
  });

  factory TradeProof.fromJson(Map<String, dynamic> json) => _$TradeProofFromJson(json);
  Map<String, dynamic> toJson() => _$TradeProofToJson(this);

  bool get isPaymentProof => type == ProofType.payment;
  bool get isIdentityProof => type == ProofType.identity;
}

enum ProofType {
  @JsonValue('payment')
  payment,
  @JsonValue('identity')
  identity,
  @JsonValue('receipt')
  receipt,
  @JsonValue('other')
  other,
}

// Trading Statistics
@JsonSerializable()
class TradingStats {
  final int totalTrades;
  final int activeTrades;
  final int completedTrades;
  final double totalVolume;
  final double averageRating;
  final int activeOffers;
  final Map<String, double> volumeByCurrency;
  final DateTime lastUpdated;

  const TradingStats({
    required this.totalTrades,
    required this.activeTrades,
    required this.completedTrades,
    required this.totalVolume,
    required this.averageRating,
    required this.activeOffers,
    required this.volumeByCurrency,
    required this.lastUpdated,
  });

  factory TradingStats.fromJson(Map<String, dynamic> json) => _$TradingStatsFromJson(json);
  Map<String, dynamic> toJson() => _$TradingStatsToJson(this);

  double get successRate => totalTrades > 0 ? (completedTrades / totalTrades) * 100 : 0;
}

// Create Offer Request
@JsonSerializable()
class CreateOfferRequest {
  final OfferType type;
  final String cryptocurrency;
  final String fiatCurrency;
  final double amount;
  final double minAmount;
  final double maxAmount;
  final double? price;
  final double? margin;
  final List<String> paymentMethods;
  final String? terms;
  final String? location;

  const CreateOfferRequest({
    required this.type,
    required this.cryptocurrency,
    required this.fiatCurrency,
    required this.amount,
    required this.minAmount,
    required this.maxAmount,
    this.price,
    this.margin,
    required this.paymentMethods,
    this.terms,
    this.location,
  });

  factory CreateOfferRequest.fromJson(Map<String, dynamic> json) => _$CreateOfferRequestFromJson(json);
  Map<String, dynamic> toJson() => _$CreateOfferRequestToJson(this);
}

// Accept Trade Request
@JsonSerializable()
class AcceptTradeRequest {
  final String offerId;
  final double amount;
  final String paymentMethod;
  final String? message;

  const AcceptTradeRequest({
    required this.offerId,
    required this.amount,
    required this.paymentMethod,
    this.message,
  });

  factory AcceptTradeRequest.fromJson(Map<String, dynamic> json) => _$AcceptTradeRequestFromJson(json);
  Map<String, dynamic> toJson() => _$AcceptTradeRequestToJson(this);
}
