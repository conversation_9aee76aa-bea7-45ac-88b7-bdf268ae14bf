import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../models/trading_models.dart';

class ActiveTradesList extends StatelessWidget {
  final List<Trade> trades;
  final bool isLoading;
  final Function(Trade) onTradeTap;
  final Future<void> Function()? onRefresh;

  const ActiveTradesList({
    super.key,
    required this.trades,
    this.isLoading = false,
    required this.onTradeTap,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading && trades.isEmpty) {
      return _buildLoadingState();
    }

    if (trades.isEmpty) {
      return _buildEmptyState(context);
    }

    return RefreshIndicator(
      onRefresh: onRefresh ?? () async {},
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: trades.length,
        itemBuilder: (context, index) {
          final trade = trades[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: TradeCard(
              trade: trade,
              onTap: () => onTradeTap(trade),
            ),
          );
        },
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text('Loading trades...'),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.swap_horiz_outlined,
              size: 80,
              color: theme.colorScheme.outline.withOpacity(0.5),
            ),
            const SizedBox(height: 24),
            Text(
              'No Active Trades',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.outline,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Accept an offer to start trading',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.outline,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class TradeCard extends StatelessWidget {
  final Trade trade;
  final VoidCallback onTap;

  const TradeCard({
    super.key,
    required this.trade,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currencyFormat = NumberFormat.currency(symbol: '', decimalDigits: 2);
    final cryptoFormat = NumberFormat('#,##0.########');

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: _getStatusColor(trade.status).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with status and time
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getStatusColor(trade.status).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      _getStatusText(trade.status),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: _getStatusColor(trade.status),
                        fontWeight: FontWeight.bold,
                        fontSize: 10,
                      ),
                    ),
                  ),
                  const Spacer(),
                  Text(
                    timeago.format(trade.createdAt),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.outline,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Trade details
              Row(
                children: [
                  // Crypto info
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getCryptoColor(trade.cryptocurrency).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _getCryptoIcon(trade.cryptocurrency),
                          size: 16,
                          color: _getCryptoColor(trade.cryptocurrency),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          trade.cryptocurrency,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: _getCryptoColor(trade.cryptocurrency),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 12),
                  // Trade type indicator
                  Icon(
                    trade.offer.type == OfferType.buy ? Icons.shopping_cart : Icons.sell,
                    size: 16,
                    color: trade.offer.type == OfferType.buy ? Colors.green : Colors.orange,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    trade.offer.type == OfferType.buy ? 'Buying' : 'Selling',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: trade.offer.type == OfferType.buy ? Colors.green : Colors.orange,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Amount and price
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Amount',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.outline,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${currencyFormat.format(trade.amount)} ${trade.fiatCurrency}',
                          style: theme.textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Crypto Amount',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.outline,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${cryptoFormat.format(trade.cryptoAmount)} ${trade.cryptocurrency}',
                          style: theme.textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Trading partner and payment method
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Trading with',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.outline,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            CircleAvatar(
                              radius: 12,
                              backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
                              child: Text(
                                _getPartnerName(trade).substring(0, 1).toUpperCase(),
                                style: TextStyle(
                                  color: theme.colorScheme.primary,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              _getPartnerName(trade),
                              style: theme.textTheme.bodySmall?.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Payment',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.outline,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.surfaceVariant.withOpacity(0.5),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            trade.paymentMethod,
                            style: theme.textTheme.bodySmall?.copyWith(
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              // Progress indicator for trade status
              if (trade.status != TradeStatus.completed && trade.status != TradeStatus.cancelled) ...[
                const SizedBox(height: 16),
                _buildProgressIndicator(context, trade.status),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProgressIndicator(BuildContext context, TradeStatus status) {
    final theme = Theme.of(context);
    final steps = [
      TradeStatus.pending,
      TradeStatus.accepted,
      TradeStatus.escrowed,
      TradeStatus.paid,
      TradeStatus.completed,
    ];
    
    final currentIndex = steps.indexOf(status);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Progress',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.outline,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: steps.asMap().entries.map((entry) {
            final index = entry.key;
            final isActive = index <= currentIndex;
            final isLast = index == steps.length - 1;
            
            return Expanded(
              child: Row(
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: isActive 
                          ? _getStatusColor(status)
                          : theme.colorScheme.outline.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  if (!isLast)
                    Expanded(
                      child: Container(
                        height: 2,
                        color: isActive 
                            ? _getStatusColor(status).withOpacity(0.3)
                            : theme.colorScheme.outline.withOpacity(0.2),
                      ),
                    ),
                ],
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  String _getPartnerName(Trade trade) {
    // This would need to be determined based on current user context
    // For now, return the buyer's name as placeholder
    return trade.buyer.displayName;
  }

  Color _getStatusColor(TradeStatus status) {
    switch (status) {
      case TradeStatus.pending:
        return Colors.orange;
      case TradeStatus.accepted:
        return Colors.blue;
      case TradeStatus.funded:
        return Colors.teal;
      case TradeStatus.escrowed:
        return Colors.purple;
      case TradeStatus.paymentSent:
        return Colors.cyan;
      case TradeStatus.paid:
        return Colors.indigo;
      case TradeStatus.completed:
        return Colors.green;
      case TradeStatus.cancelled:
        return Colors.red;
      case TradeStatus.disputed:
        return Colors.red;
      case TradeStatus.expired:
        return Colors.grey;
    }
  }

  String _getStatusText(TradeStatus status) {
    switch (status) {
      case TradeStatus.pending:
        return 'PENDING';
      case TradeStatus.accepted:
        return 'ACCEPTED';
      case TradeStatus.funded:
        return 'FUNDED';
      case TradeStatus.escrowed:
        return 'ESCROWED';
      case TradeStatus.paymentSent:
        return 'PAYMENT SENT';
      case TradeStatus.paid:
        return 'PAID';
      case TradeStatus.completed:
        return 'COMPLETED';
      case TradeStatus.cancelled:
        return 'CANCELLED';
      case TradeStatus.disputed:
        return 'DISPUTED';
      case TradeStatus.expired:
        return 'EXPIRED';
    }
  }

  IconData _getCryptoIcon(String symbol) {
    switch (symbol.toUpperCase()) {
      case 'BTC':
        return Icons.currency_bitcoin;
      case 'ETH':
        return Icons.diamond;
      case 'MATIC':
        return Icons.hexagon;
      case 'USDT':
      case 'USDC':
        return Icons.attach_money;
      case 'DAI':
        return Icons.account_balance;
      default:
        return Icons.monetization_on;
    }
  }

  Color _getCryptoColor(String symbol) {
    switch (symbol.toUpperCase()) {
      case 'BTC':
        return const Color(0xFFF7931A);
      case 'ETH':
        return const Color(0xFF627EEA);
      case 'MATIC':
        return const Color(0xFF8247E5);
      case 'USDT':
        return const Color(0xFF26A17B);
      case 'USDC':
        return const Color(0xFF2775CA);
      case 'DAI':
        return const Color(0xFFF5AC37);
      default:
        return const Color(0xFF6B7280);
    }
  }
}
