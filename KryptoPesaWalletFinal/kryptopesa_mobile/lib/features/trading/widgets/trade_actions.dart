import 'package:flutter/material.dart';
import '../models/trading_models.dart';

class TradeActions extends StatelessWidget {
  final Trade trade;
  final Function(String) onAction;

  const TradeActions({
    super.key,
    required this.trade,
    required this.onAction,
  });

  @override
  Widget build(BuildContext context) {
    final actions = _getAvailableActions();
    
    if (actions.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Column(
      children: actions.map((action) => Padding(
        padding: const EdgeInsets.only(bottom: 8),
        child: _buildActionButton(context, action),
      )).toList(),
    );
  }

  List<TradeAction> _getAvailableActions() {
    final actions = <TradeAction>[];
    
    switch (trade.status) {
      case TradeStatus.pending:
        if (trade.isUserSeller) {
          actions.add(TradeAction(
            id: 'fund_escrow',
            label: 'Fund Escrow',
            description: 'Transfer ${trade.amount} ${trade.cryptocurrency} to escrow',
            icon: Icons.account_balance_wallet,
            color: Colors.blue,
            isPrimary: true,
          ));
        }
        actions.add(TradeAction(
          id: 'cancel_trade',
          label: 'Cancel Trade',
          description: 'Cancel this trade',
          icon: Icons.cancel,
          color: Colors.red,
          isDestructive: true,
        ));
        break;
        
      case TradeStatus.funded:
        if (trade.isUserBuyer) {
          actions.add(TradeAction(
            id: 'mark_payment_sent',
            label: 'Mark Payment Sent',
            description: 'Confirm you have sent the payment',
            icon: Icons.send,
            color: Colors.green,
            isPrimary: true,
          ));
          actions.add(TradeAction(
            id: 'upload_payment_proof',
            label: 'Upload Payment Proof',
            description: 'Upload receipt or screenshot',
            icon: Icons.upload_file,
            color: Colors.blue,
          ));
        }
        actions.add(TradeAction(
          id: 'cancel_trade',
          label: 'Cancel Trade',
          description: 'Cancel this trade',
          icon: Icons.cancel,
          color: Colors.red,
          isDestructive: true,
        ));
        break;
        
      case TradeStatus.paymentSent:
        if (trade.isUserSeller) {
          actions.add(TradeAction(
            id: 'confirm_payment',
            label: 'Confirm Payment Received',
            description: 'Confirm you received the payment',
            icon: Icons.check_circle,
            color: Colors.green,
            isPrimary: true,
          ));
          actions.add(TradeAction(
            id: 'view_payment_proof',
            label: 'View Payment Proof',
            description: 'Check uploaded payment evidence',
            icon: Icons.receipt,
            color: Colors.blue,
          ));
        }
        actions.add(TradeAction(
          id: 'create_dispute',
          label: 'Create Dispute',
          description: 'Report an issue with this trade',
          icon: Icons.gavel,
          color: Colors.orange,
        ));
        break;
        
      case TradeStatus.completed:
        actions.add(TradeAction(
          id: 'rate_counterparty',
          label: 'Rate Trading Partner',
          description: 'Leave feedback for your trading partner',
          icon: Icons.star,
          color: Colors.amber,
        ));
        actions.add(TradeAction(
          id: 'view_receipt',
          label: 'View Trade Receipt',
          description: 'Download trade completion receipt',
          icon: Icons.receipt_long,
          color: Colors.blue,
        ));
        break;
        
      case TradeStatus.disputed:
        actions.add(TradeAction(
          id: 'view_dispute',
          label: 'View Dispute Details',
          description: 'Check dispute status and resolution',
          icon: Icons.gavel,
          color: Colors.red,
        ));
        actions.add(TradeAction(
          id: 'add_evidence',
          label: 'Add Evidence',
          description: 'Submit additional evidence',
          icon: Icons.add_photo_alternate,
          color: Colors.blue,
        ));
        break;
        
      default:
        break;
    }
    
    // Always available actions
    actions.add(TradeAction(
      id: 'contact_support',
      label: 'Contact Support',
      description: 'Get help with this trade',
      icon: Icons.support_agent,
      color: Colors.grey,
    ));
    
    return actions;
  }

  Widget _buildActionButton(BuildContext context, TradeAction action) {
    final theme = Theme.of(context);
    
    if (action.isPrimary) {
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: () => _handleAction(context, action),
          icon: Icon(action.icon),
          label: Text(action.label),
          style: ElevatedButton.styleFrom(
            backgroundColor: action.color,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      );
    }
    
    if (action.isDestructive) {
      return SizedBox(
        width: double.infinity,
        child: OutlinedButton.icon(
          onPressed: () => _handleAction(context, action),
          icon: Icon(action.icon),
          label: Text(action.label),
          style: OutlinedButton.styleFrom(
            foregroundColor: action.color,
            side: BorderSide(color: action.color),
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      );
    }
    
    return SizedBox(
      width: double.infinity,
      child: TextButton.icon(
        onPressed: () => _handleAction(context, action),
        icon: Icon(action.icon, color: action.color),
        label: Text(
          action.label,
          style: TextStyle(color: action.color),
        ),
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(
              color: action.color.withOpacity(0.3),
            ),
          ),
        ),
      ),
    );
  }

  void _handleAction(BuildContext context, TradeAction action) {
    if (action.isDestructive) {
      _showConfirmationDialog(context, action);
    } else {
      onAction(action.id);
    }
  }

  void _showConfirmationDialog(BuildContext context, TradeAction action) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Confirm ${action.label}'),
        content: Text('Are you sure you want to ${action.label.toLowerCase()}? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              onAction(action.id);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: action.color,
              foregroundColor: Colors.white,
            ),
            child: Text(action.label),
          ),
        ],
      ),
    );
  }
}

class TradeAction {
  final String id;
  final String label;
  final String description;
  final IconData icon;
  final Color color;
  final bool isPrimary;
  final bool isDestructive;

  TradeAction({
    required this.id,
    required this.label,
    required this.description,
    required this.icon,
    required this.color,
    this.isPrimary = false,
    this.isDestructive = false,
  });
}
