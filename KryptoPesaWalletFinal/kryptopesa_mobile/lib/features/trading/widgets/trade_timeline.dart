import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/trading_models.dart';

class TradeTimeline extends StatelessWidget {
  final Trade trade;

  const TradeTimeline({
    super.key,
    required this.trade,
  });

  @override
  Widget build(BuildContext context) {
    final events = _buildTimelineEvents();
    
    return Column(
      children: events.asMap().entries.map((entry) {
        final index = entry.key;
        final event = entry.value;
        final isLast = index == events.length - 1;
        
        return _buildTimelineItem(
          context,
          event,
          isLast: isLast,
        );
      }).toList(),
    );
  }

  List<TimelineEvent> _buildTimelineEvents() {
    final events = <TimelineEvent>[];
    
    // Trade created
    events.add(TimelineEvent(
      title: 'Trade Created',
      description: 'Trade initiated between parties',
      timestamp: trade.createdAt,
      icon: Icons.handshake,
      color: Colors.blue,
      isCompleted: true,
    ));
    
    // Escrow funding
    if (trade.escrowFundedAt != null) {
      events.add(TimelineEvent(
        title: 'Escrow Funded',
        description: 'Seller funded the escrow with ${trade.amount} ${trade.cryptocurrency}',
        timestamp: trade.escrowFundedAt!,
        icon: Icons.account_balance_wallet,
        color: Colors.green,
        isCompleted: true,
      ));
    } else if (trade.status == TradeStatus.pending) {
      events.add(TimelineEvent(
        title: 'Escrow Funding',
        description: 'Waiting for seller to fund escrow',
        timestamp: null,
        icon: Icons.account_balance_wallet,
        color: Colors.orange,
        isCompleted: false,
        isPending: true,
      ));
    }
    
    // Payment sent
    if (trade.paymentSentAt != null) {
      events.add(TimelineEvent(
        title: 'Payment Sent',
        description: 'Buyer sent ${(trade.amount * trade.price).toStringAsFixed(2)} ${trade.fiatCurrency} via ${trade.paymentMethod}',
        timestamp: trade.paymentSentAt!,
        icon: Icons.send,
        color: Colors.purple,
        isCompleted: true,
      ));
    } else if (trade.status == TradeStatus.funded) {
      events.add(TimelineEvent(
        title: 'Payment Pending',
        description: 'Waiting for buyer to send payment',
        timestamp: null,
        icon: Icons.send,
        color: Colors.orange,
        isCompleted: false,
        isPending: true,
      ));
    }
    
    // Payment confirmed
    if (trade.paymentConfirmedAt != null) {
      events.add(TimelineEvent(
        title: 'Payment Confirmed',
        description: 'Seller confirmed receipt of payment',
        timestamp: trade.paymentConfirmedAt!,
        icon: Icons.check_circle,
        color: Colors.green,
        isCompleted: true,
      ));
    } else if (trade.status == TradeStatus.paymentSent) {
      events.add(TimelineEvent(
        title: 'Payment Confirmation',
        description: 'Waiting for seller to confirm payment',
        timestamp: null,
        icon: Icons.check_circle,
        color: Colors.orange,
        isCompleted: false,
        isPending: true,
      ));
    }
    
    // Trade completed
    if (trade.completedAt != null) {
      events.add(TimelineEvent(
        title: 'Trade Completed',
        description: 'Crypto released to buyer, trade successful',
        timestamp: trade.completedAt!,
        icon: Icons.celebration,
        color: Colors.green,
        isCompleted: true,
      ));
    }
    
    // Handle cancelled/disputed/expired states
    if (trade.status == TradeStatus.cancelled && trade.cancelledAt != null) {
      events.add(TimelineEvent(
        title: 'Trade Cancelled',
        description: trade.cancellationReason ?? 'Trade was cancelled',
        timestamp: trade.cancelledAt!,
        icon: Icons.cancel,
        color: Colors.red,
        isCompleted: true,
      ));
    }
    
    if (trade.status == TradeStatus.disputed && trade.disputeCreatedAt != null) {
      events.add(TimelineEvent(
        title: 'Dispute Created',
        description: 'Trade is under dispute resolution',
        timestamp: trade.disputeCreatedAt!,
        icon: Icons.gavel,
        color: Colors.red,
        isCompleted: true,
      ));
    }
    
    if (trade.status == TradeStatus.expired) {
      events.add(TimelineEvent(
        title: 'Trade Expired',
        description: 'Trade expired due to timeout',
        timestamp: trade.expiresAt ?? DateTime.now(),
        icon: Icons.timer_off,
        color: Colors.grey,
        isCompleted: true,
      ));
    }
    
    return events;
  }

  Widget _buildTimelineItem(
    BuildContext context,
    TimelineEvent event, {
    required bool isLast,
  }) {
    final theme = Theme.of(context);
    
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timeline indicator
        Column(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: event.isCompleted 
                    ? event.color 
                    : event.isPending 
                        ? event.color.withOpacity(0.3)
                        : theme.colorScheme.surfaceVariant,
                shape: BoxShape.circle,
                border: event.isPending 
                    ? Border.all(color: event.color, width: 2)
                    : null,
              ),
              child: Icon(
                event.icon,
                size: 16,
                color: event.isCompleted 
                    ? Colors.white
                    : event.isPending
                        ? event.color
                        : theme.colorScheme.onSurfaceVariant,
              ),
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 40,
                color: event.isCompleted 
                    ? event.color.withOpacity(0.3)
                    : theme.colorScheme.surfaceVariant,
              ),
          ],
        ),
        
        const SizedBox(width: 16),
        
        // Event content
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(bottom: isLast ? 0 : 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        event.title,
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: event.isCompleted 
                              ? null 
                              : theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ),
                    if (event.timestamp != null)
                      Text(
                        _formatTimestamp(event.timestamp!),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  event.description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                if (event.isPending) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      SizedBox(
                        width: 12,
                        height: 12,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(event.color),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'In Progress',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: event.color,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return DateFormat('MMM d, HH:mm').format(timestamp);
    }
  }
}

class TimelineEvent {
  final String title;
  final String description;
  final DateTime? timestamp;
  final IconData icon;
  final Color color;
  final bool isCompleted;
  final bool isPending;

  TimelineEvent({
    required this.title,
    required this.description,
    this.timestamp,
    required this.icon,
    required this.color,
    this.isCompleted = false,
    this.isPending = false,
  });
}
