import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';
import '../models/trading_models.dart';

class OfferFilterSheet extends StatefulWidget {
  final OfferType? selectedOfferType;
  final String? selectedCryptocurrency;
  final String? selectedFiatCurrency;
  final List<String> selectedPaymentMethods;
  final double? minAmount;
  final double? maxAmount;
  final String sortBy;
  final bool sortAscending;
  final Function(Map<String, dynamic>) onApplyFilters;
  final VoidCallback onClearFilters;

  const OfferFilterSheet({
    super.key,
    this.selectedOfferType,
    this.selectedCryptocurrency,
    this.selectedFiatCurrency,
    required this.selectedPaymentMethods,
    this.minAmount,
    this.maxAmount,
    required this.sortBy,
    required this.sortAscending,
    required this.onApplyFilters,
    required this.onClearFilters,
  });

  @override
  State<OfferFilterSheet> createState() => _OfferFilterSheetState();
}

class _OfferFilterSheetState extends State<OfferFilterSheet> {
  late OfferType? _offerType;
  late String? _cryptocurrency;
  late String? _fiatCurrency;
  late List<String> _paymentMethods;
  late double? _minAmount;
  late double? _maxAmount;
  late String _sortBy;
  late bool _sortAscending;

  final _minAmountController = TextEditingController();
  final _maxAmountController = TextEditingController();

  // Available options
  final List<String> _cryptocurrencies = ['USDT', 'USDC', 'BTC', 'ETH', 'MATIC'];
  final List<String> _fiatCurrencies = ['KES', 'UGX', 'TZS', 'RWF', 'USD'];
  final List<String> _paymentMethodOptions = [
    'M-Pesa',
    'Airtel Money',
    'Bank Transfer',
    'Cash',
    'Tigo Pesa',
    'MTN Mobile Money',
  ];
  final List<String> _sortOptions = ['price', 'amount', 'rating', 'trades', 'created'];

  @override
  void initState() {
    super.initState();
    _offerType = widget.selectedOfferType;
    _cryptocurrency = widget.selectedCryptocurrency;
    _fiatCurrency = widget.selectedFiatCurrency;
    _paymentMethods = List.from(widget.selectedPaymentMethods);
    _minAmount = widget.minAmount;
    _maxAmount = widget.maxAmount;
    _sortBy = widget.sortBy;
    _sortAscending = widget.sortAscending;

    _minAmountController.text = _minAmount?.toString() ?? '';
    _maxAmountController.text = _maxAmount?.toString() ?? '';
  }

  @override
  void dispose() {
    _minAmountController.dispose();
    _maxAmountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: colorScheme.onSurfaceVariant.withOpacity(0.4),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Text(
                  'Filter Offers',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _clearFilters,
                  child: Text('Clear All'),
                ),
              ],
            ),
          ),

          // Filters content
          Flexible(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Offer Type
                  _buildSectionTitle('Offer Type'),
                  _buildOfferTypeSelector(),
                  const SizedBox(height: 24),

                  // Cryptocurrency
                  _buildSectionTitle('Cryptocurrency'),
                  _buildCryptocurrencySelector(),
                  const SizedBox(height: 24),

                  // Fiat Currency
                  _buildSectionTitle('Fiat Currency'),
                  _buildFiatCurrencySelector(),
                  const SizedBox(height: 24),

                  // Amount Range
                  _buildSectionTitle('Amount Range'),
                  _buildAmountRangeInputs(),
                  const SizedBox(height: 24),

                  // Payment Methods
                  _buildSectionTitle('Payment Methods'),
                  _buildPaymentMethodsSelector(),
                  const SizedBox(height: 24),

                  // Sort Options
                  _buildSectionTitle('Sort By'),
                  _buildSortOptions(),
                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),

          // Action buttons
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(
                  color: colorScheme.outline.withOpacity(0.2),
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _applyFilters,
                    child: Text('Apply Filters'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildOfferTypeSelector() {
    return Row(
      children: [
        Expanded(
          child: _buildChoiceChip(
            label: 'All',
            selected: _offerType == null,
            onSelected: (selected) {
              if (selected) setState(() => _offerType = null);
            },
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildChoiceChip(
            label: 'Buy',
            selected: _offerType == OfferType.buy,
            onSelected: (selected) {
              if (selected) setState(() => _offerType = OfferType.buy);
            },
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildChoiceChip(
            label: 'Sell',
            selected: _offerType == OfferType.sell,
            onSelected: (selected) {
              if (selected) setState(() => _offerType = OfferType.sell);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCryptocurrencySelector() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        _buildChoiceChip(
          label: 'All',
          selected: _cryptocurrency == null,
          onSelected: (selected) {
            if (selected) setState(() => _cryptocurrency = null);
          },
        ),
        ..._cryptocurrencies.map((crypto) => _buildChoiceChip(
          label: crypto,
          selected: _cryptocurrency == crypto,
          onSelected: (selected) {
            setState(() => _cryptocurrency = selected ? crypto : null);
          },
        )),
      ],
    );
  }

  Widget _buildFiatCurrencySelector() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        _buildChoiceChip(
          label: 'All',
          selected: _fiatCurrency == null,
          onSelected: (selected) {
            if (selected) setState(() => _fiatCurrency = null);
          },
        ),
        ..._fiatCurrencies.map((fiat) => _buildChoiceChip(
          label: fiat,
          selected: _fiatCurrency == fiat,
          onSelected: (selected) {
            setState(() => _fiatCurrency = selected ? fiat : null);
          },
        )),
      ],
    );
  }

  Widget _buildAmountRangeInputs() {
    return Row(
      children: [
        Expanded(
          child: TextField(
            controller: _minAmountController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              labelText: 'Min Amount',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onChanged: (value) {
              _minAmount = double.tryParse(value);
            },
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: TextField(
            controller: _maxAmountController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              labelText: 'Max Amount',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onChanged: (value) {
              _maxAmount = double.tryParse(value);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentMethodsSelector() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: _paymentMethodOptions.map((method) => _buildChoiceChip(
        label: method,
        selected: _paymentMethods.contains(method),
        onSelected: (selected) {
          setState(() {
            if (selected) {
              _paymentMethods.add(method);
            } else {
              _paymentMethods.remove(method);
            }
          });
        },
      )).toList(),
    );
  }

  Widget _buildSortOptions() {
    return Column(
      children: [
        DropdownButtonFormField<String>(
          value: _sortBy,
          decoration: InputDecoration(
            labelText: 'Sort by',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          items: _sortOptions.map((option) {
            return DropdownMenuItem(
              value: option,
              child: Text(_getSortDisplayName(option)),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() => _sortBy = value);
            }
          },
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildChoiceChip(
                label: 'Ascending',
                selected: _sortAscending,
                onSelected: (selected) {
                  if (selected) setState(() => _sortAscending = true);
                },
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildChoiceChip(
                label: 'Descending',
                selected: !_sortAscending,
                onSelected: (selected) {
                  if (selected) setState(() => _sortAscending = false);
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildChoiceChip({
    required String label,
    required bool selected,
    required Function(bool) onSelected,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return FilterChip(
      label: Text(label),
      selected: selected,
      onSelected: onSelected,
      backgroundColor: colorScheme.surface,
      selectedColor: colorScheme.primaryContainer,
      checkmarkColor: colorScheme.onPrimaryContainer,
      labelStyle: TextStyle(
        color: selected 
            ? colorScheme.onPrimaryContainer 
            : colorScheme.onSurface,
      ),
      side: BorderSide(
        color: selected 
            ? colorScheme.primary 
            : colorScheme.outline,
      ),
    );
  }

  String _getSortDisplayName(String sortBy) {
    switch (sortBy) {
      case 'price':
        return 'Price';
      case 'amount':
        return 'Amount';
      case 'rating':
        return 'User Rating';
      case 'trades':
        return 'Completed Trades';
      case 'created':
        return 'Date Created';
      default:
        return sortBy;
    }
  }

  void _clearFilters() {
    setState(() {
      _offerType = null;
      _cryptocurrency = null;
      _fiatCurrency = null;
      _paymentMethods.clear();
      _minAmount = null;
      _maxAmount = null;
      _sortBy = 'price';
      _sortAscending = true;
      _minAmountController.clear();
      _maxAmountController.clear();
    });
    widget.onClearFilters();
  }

  void _applyFilters() {
    widget.onApplyFilters({
      'offerType': _offerType,
      'cryptocurrency': _cryptocurrency,
      'fiatCurrency': _fiatCurrency,
      'paymentMethods': _paymentMethods,
      'minAmount': _minAmount,
      'maxAmount': _maxAmount,
      'sortBy': _sortBy,
      'sortAscending': _sortAscending,
    });
    Navigator.pop(context);
  }
}
