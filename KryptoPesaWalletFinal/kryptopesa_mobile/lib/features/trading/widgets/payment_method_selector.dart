import 'package:flutter/material.dart';

class PaymentMethodSelector extends StatefulWidget {
  final List<String> selectedMethods;
  final Function(List<String>) onMethodsChanged;

  const PaymentMethodSelector({
    super.key,
    required this.selectedMethods,
    required this.onMethodsChanged,
  });

  @override
  State<PaymentMethodSelector> createState() => _PaymentMethodSelectorState();
}

class _PaymentMethodSelectorState extends State<PaymentMethodSelector> {
  final List<PaymentMethodOption> _paymentMethods = [
    PaymentMethodOption(
      id: 'mpesa',
      name: 'M-Pesa',
      description: 'Kenya mobile money',
      icon: Icons.phone_android,
      color: Colors.green,
      isPopular: true,
    ),
    PaymentMethodOption(
      id: 'airtel_money',
      name: 'Airtel Money',
      description: 'Airtel mobile money',
      icon: Icons.phone_android,
      color: Colors.red,
      isPopular: true,
    ),
    PaymentMethodOption(
      id: 'bank_transfer',
      name: 'Bank Transfer',
      description: 'Direct bank transfer',
      icon: Icons.account_balance,
      color: Colors.blue,
      isPopular: true,
    ),
    PaymentMethodOption(
      id: 'tigo_pesa',
      name: 'Tigo Pesa',
      description: 'Tanzania mobile money',
      icon: Icons.phone_android,
      color: Colors.orange,
    ),
    PaymentMethodOption(
      id: 'mtn_mobile_money',
      name: 'MTN Mobile Money',
      description: 'Uganda mobile money',
      icon: Icons.phone_android,
      color: Colors.yellow,
    ),
    PaymentMethodOption(
      id: 'cash',
      name: 'Cash',
      description: 'Cash in person',
      icon: Icons.money,
      color: Colors.grey,
    ),
    PaymentMethodOption(
      id: 'paypal',
      name: 'PayPal',
      description: 'PayPal transfer',
      icon: Icons.payment,
      color: Colors.indigo,
    ),
    PaymentMethodOption(
      id: 'wise',
      name: 'Wise',
      description: 'Wise transfer',
      icon: Icons.send,
      color: Colors.teal,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final popularMethods = _paymentMethods.where((m) => m.isPopular).toList();
    final otherMethods = _paymentMethods.where((m) => !m.isPopular).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Popular methods
        if (popularMethods.isNotEmpty) ...[
          Text(
            'Popular Methods',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          ...popularMethods.map((method) => _buildPaymentMethodTile(method)),
          const SizedBox(height: 24),
        ],

        // Other methods
        if (otherMethods.isNotEmpty) ...[
          Text(
            'Other Methods',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          ...otherMethods.map((method) => _buildPaymentMethodTile(method)),
        ],

        // Selected methods summary
        if (widget.selectedMethods.isNotEmpty) ...[
          const SizedBox(height: 24),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer.withOpacity(0.3),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: theme.colorScheme.primary.withOpacity(0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: theme.colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Selected Payment Methods',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 4,
                  children: widget.selectedMethods.map((methodId) {
                    final method = _paymentMethods.firstWhere(
                      (m) => m.id == methodId,
                      orElse: () => PaymentMethodOption(
                        id: methodId,
                        name: methodId,
                        description: '',
                        icon: Icons.payment,
                        color: Colors.grey,
                      ),
                    );
                    return Chip(
                      label: Text(method.name),
                      avatar: Icon(
                        method.icon,
                        size: 16,
                        color: method.color,
                      ),
                      backgroundColor: theme.colorScheme.surface,
                      side: BorderSide(
                        color: theme.colorScheme.outline.withOpacity(0.5),
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildPaymentMethodTile(PaymentMethodOption method) {
    final theme = Theme.of(context);
    final isSelected = widget.selectedMethods.contains(method.id);

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _togglePaymentMethod(method.id),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(
                color: isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.outline.withOpacity(0.3),
                width: isSelected ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(12),
              color: isSelected
                  ? theme.colorScheme.primaryContainer.withOpacity(0.1)
                  : null,
            ),
            child: Row(
              children: [
                // Method icon
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: method.color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    method.icon,
                    color: method.color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),

                // Method details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            method.name,
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: isSelected ? theme.colorScheme.primary : null,
                            ),
                          ),
                          if (method.isPopular) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.orange.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                'Popular',
                                style: theme.textTheme.labelSmall?.copyWith(
                                  color: Colors.orange,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        method.description,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),

                // Selection indicator
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected
                          ? theme.colorScheme.primary
                          : theme.colorScheme.outline,
                      width: 2,
                    ),
                    color: isSelected ? theme.colorScheme.primary : null,
                  ),
                  child: isSelected
                      ? Icon(
                          Icons.check,
                          size: 16,
                          color: theme.colorScheme.onPrimary,
                        )
                      : null,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _togglePaymentMethod(String methodId) {
    final updatedMethods = List<String>.from(widget.selectedMethods);
    
    if (updatedMethods.contains(methodId)) {
      updatedMethods.remove(methodId);
    } else {
      updatedMethods.add(methodId);
    }
    
    widget.onMethodsChanged(updatedMethods);
  }
}

class PaymentMethodOption {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final Color color;
  final bool isPopular;

  PaymentMethodOption({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    this.isPopular = false,
  });
}
