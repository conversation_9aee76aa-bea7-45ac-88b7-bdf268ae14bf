import 'package:flutter/material.dart';
import '../models/trading_models.dart';

class TradeStatusIndicator extends StatelessWidget {
  final TradeStatus status;
  final bool showLabel;

  const TradeStatusIndicator({
    super.key,
    required this.status,
    this.showLabel = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final statusInfo = _getStatusInfo(status);
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: statusInfo.color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: statusInfo.color,
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                statusInfo.icon,
                size: 16,
                color: statusInfo.color,
              ),
              if (showLabel) ...[
                const SizedBox(width: 6),
                Text(
                  statusInfo.label,
                  style: theme.textTheme.labelSmall?.copyWith(
                    color: statusInfo.color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  TradeStatusInfo _getStatusInfo(TradeStatus status) {
    switch (status) {
      case TradeStatus.pending:
        return TradeStatusInfo(
          label: 'Pending',
          icon: Icons.hourglass_empty,
          color: Colors.orange,
        );
      case TradeStatus.funded:
        return TradeStatusInfo(
          label: 'Funded',
          icon: Icons.account_balance_wallet,
          color: Colors.blue,
        );
      case TradeStatus.paymentSent:
        return TradeStatusInfo(
          label: 'Payment Sent',
          icon: Icons.send,
          color: Colors.purple,
        );
      case TradeStatus.completed:
        return TradeStatusInfo(
          label: 'Completed',
          icon: Icons.check_circle,
          color: Colors.green,
        );
      case TradeStatus.cancelled:
        return TradeStatusInfo(
          label: 'Cancelled',
          icon: Icons.cancel,
          color: Colors.red,
        );
      case TradeStatus.disputed:
        return TradeStatusInfo(
          label: 'Disputed',
          icon: Icons.gavel,
          color: Colors.red,
        );
      case TradeStatus.expired:
        return TradeStatusInfo(
          label: 'Expired',
          icon: Icons.timer_off,
          color: Colors.grey,
        );
      default:
        return TradeStatusInfo(
          label: 'Unknown',
          icon: Icons.help,
          color: Colors.grey,
        );
    }
  }
}

class TradeStatusInfo {
  final String label;
  final IconData icon;
  final Color color;

  TradeStatusInfo({
    required this.label,
    required this.icon,
    required this.color,
  });
}
