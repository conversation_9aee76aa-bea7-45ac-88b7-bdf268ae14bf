import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/loading_overlay.dart';
import '../../../core/widgets/error_display.dart';
import '../../../core/widgets/empty_state.dart';
import '../models/trading_models.dart';
import '../providers/trading_provider.dart';
import '../widgets/offer_card.dart';
import '../widgets/offer_filter_sheet.dart';

class OfferListScreen extends ConsumerStatefulWidget {
  final OfferType? offerType;
  final String? cryptocurrency;

  const OfferListScreen({
    super.key,
    this.offerType,
    this.cryptocurrency,
  });

  @override
  ConsumerState<OfferListScreen> createState() => _OfferListScreenState();
}

class _OfferListScreenState extends ConsumerState<OfferListScreen> {
  final _searchController = TextEditingController();
  final _scrollController = ScrollController();
  
  // Filter state
  OfferType? _selectedOfferType;
  String? _selectedCryptocurrency;
  String? _selectedFiatCurrency;
  List<String> _selectedPaymentMethods = [];
  double? _minAmount;
  double? _maxAmount;
  String _sortBy = 'price';
  bool _sortAscending = true;
  
  @override
  void initState() {
    super.initState();
    _selectedOfferType = widget.offerType;
    _selectedCryptocurrency = widget.cryptocurrency;
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadOffers();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadOffers() async {
    await ref.read(tradingProvider.notifier).refreshOffers();
  }

  Future<void> _refreshOffers() async {
    await _loadOffers();
  }

  List<TradingOffer> _getFilteredOffers(List<TradingOffer> offers) {
    var filtered = offers.where((offer) => offer.isActive).toList();
    
    // Apply filters
    if (_selectedOfferType != null) {
      filtered = filtered.where((o) => o.type == _selectedOfferType).toList();
    }
    
    if (_selectedCryptocurrency != null) {
      filtered = filtered.where((o) => o.cryptocurrency == _selectedCryptocurrency).toList();
    }
    
    if (_selectedFiatCurrency != null) {
      filtered = filtered.where((o) => o.fiatCurrency == _selectedFiatCurrency).toList();
    }
    
    if (_selectedPaymentMethods.isNotEmpty) {
      filtered = filtered.where((o) => 
        o.paymentMethods.any((pm) => _selectedPaymentMethods.contains(pm))
      ).toList();
    }
    
    if (_minAmount != null) {
      filtered = filtered.where((o) => o.maxAmount >= _minAmount!).toList();
    }
    
    if (_maxAmount != null) {
      filtered = filtered.where((o) => o.minAmount <= _maxAmount!).toList();
    }
    
    // Apply search
    final searchTerm = _searchController.text.toLowerCase();
    if (searchTerm.isNotEmpty) {
      filtered = filtered.where((o) => 
        o.username.toLowerCase().contains(searchTerm) ||
        o.cryptocurrency.toLowerCase().contains(searchTerm) ||
        o.fiatCurrency.toLowerCase().contains(searchTerm) ||
        o.paymentMethods.any((pm) => pm.toLowerCase().contains(searchTerm))
      ).toList();
    }
    
    // Apply sorting
    filtered.sort((a, b) {
      int comparison = 0;
      switch (_sortBy) {
        case 'price':
          comparison = a.price.compareTo(b.price);
          break;
        case 'amount':
          comparison = a.amount.compareTo(b.amount);
          break;
        case 'rating':
          comparison = a.userRating.compareTo(b.userRating);
          break;
        case 'trades':
          comparison = a.completedTrades.compareTo(b.completedTrades);
          break;
        case 'created':
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
      }
      return _sortAscending ? comparison : -comparison;
    });
    
    return filtered;
  }

  void _showFilterSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => OfferFilterSheet(
        selectedOfferType: _selectedOfferType,
        selectedCryptocurrency: _selectedCryptocurrency,
        selectedFiatCurrency: _selectedFiatCurrency,
        selectedPaymentMethods: _selectedPaymentMethods,
        minAmount: _minAmount,
        maxAmount: _maxAmount,
        sortBy: _sortBy,
        sortAscending: _sortAscending,
        onApplyFilters: (filters) {
          setState(() {
            _selectedOfferType = filters['offerType'];
            _selectedCryptocurrency = filters['cryptocurrency'];
            _selectedFiatCurrency = filters['fiatCurrency'];
            _selectedPaymentMethods = filters['paymentMethods'] ?? [];
            _minAmount = filters['minAmount'];
            _maxAmount = filters['maxAmount'];
            _sortBy = filters['sortBy'] ?? 'price';
            _sortAscending = filters['sortAscending'] ?? true;
          });
        },
        onClearFilters: () {
          setState(() {
            _selectedOfferType = widget.offerType;
            _selectedCryptocurrency = widget.cryptocurrency;
            _selectedFiatCurrency = null;
            _selectedPaymentMethods = [];
            _minAmount = null;
            _maxAmount = null;
            _sortBy = 'price';
            _sortAscending = true;
          });
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final tradingState = ref.watch(tradingProvider);
    final filteredOffers = _getFilteredOffers(tradingState.offers);
    
    return Scaffold(
      appBar: CustomAppBar(
        title: _selectedOfferType == OfferType.buy ? 'Buy Offers' : 
               _selectedOfferType == OfferType.sell ? 'Sell Offers' : 'All Offers',
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterSheet,
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => context.push('/trading/create-offer'),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Container(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search offers...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {});
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onChanged: (value) => setState(() {}),
            ),
          ),
          
          // Filter chips
          if (_hasActiveFilters())
            Container(
              height: 50,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: ListView(
                scrollDirection: Axis.horizontal,
                children: _buildFilterChips(),
              ),
            ),
          
          // Offers list
          Expanded(
            child: _buildOffersList(tradingState, filteredOffers),
          ),
        ],
      ),
    );
  }

  bool _hasActiveFilters() {
    return _selectedOfferType != null ||
           _selectedCryptocurrency != null ||
           _selectedFiatCurrency != null ||
           _selectedPaymentMethods.isNotEmpty ||
           _minAmount != null ||
           _maxAmount != null ||
           _sortBy != 'price' ||
           !_sortAscending;
  }

  List<Widget> _buildFilterChips() {
    final chips = <Widget>[];
    
    if (_selectedOfferType != null) {
      chips.add(_buildFilterChip(
        _selectedOfferType == OfferType.buy ? 'Buy' : 'Sell',
        () => setState(() => _selectedOfferType = null),
      ));
    }
    
    if (_selectedCryptocurrency != null) {
      chips.add(_buildFilterChip(
        _selectedCryptocurrency!,
        () => setState(() => _selectedCryptocurrency = null),
      ));
    }
    
    if (_selectedFiatCurrency != null) {
      chips.add(_buildFilterChip(
        _selectedFiatCurrency!,
        () => setState(() => _selectedFiatCurrency = null),
      ));
    }
    
    for (final method in _selectedPaymentMethods) {
      chips.add(_buildFilterChip(
        method,
        () => setState(() => _selectedPaymentMethods.remove(method)),
      ));
    }
    
    if (_minAmount != null || _maxAmount != null) {
      final amountText = _minAmount != null && _maxAmount != null
          ? '${_minAmount!.toStringAsFixed(0)}-${_maxAmount!.toStringAsFixed(0)}'
          : _minAmount != null
              ? '>${_minAmount!.toStringAsFixed(0)}'
              : '<${_maxAmount!.toStringAsFixed(0)}';
      chips.add(_buildFilterChip(
        amountText,
        () => setState(() {
          _minAmount = null;
          _maxAmount = null;
        }),
      ));
    }
    
    return chips;
  }

  Widget _buildFilterChip(String label, VoidCallback onDeleted) {
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: Chip(
        label: Text(label),
        onDeleted: onDeleted,
        deleteIcon: const Icon(Icons.close, size: 18),
        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
        labelStyle: TextStyle(
          color: Theme.of(context).colorScheme.onPrimaryContainer,
        ),
      ),
    );
  }

  Widget _buildOffersList(TradingState state, List<TradingOffer> offers) {
    if (state.isLoading && offers.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }
    
    if (state.hasError && offers.isEmpty) {
      return ErrorDisplay(
        error: state.error!,
        onRetry: _loadOffers,
      );
    }
    
    if (offers.isEmpty) {
      return EmptyState(
        icon: Icons.local_offer_outlined,
        title: 'No offers found',
        subtitle: _hasActiveFilters()
            ? 'Try adjusting your filters'
            : 'Be the first to create an offer!',
        actionText: 'Create Offer',
        onAction: () => context.push('/trading/create-offer'),
      );
    }
    
    return RefreshIndicator(
      onRefresh: _refreshOffers,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        itemCount: offers.length,
        itemBuilder: (context, index) {
          final offer = offers[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: OfferCard(
              offer: offer,
              onTap: () => _handleOfferTap(offer),
            ),
          );
        },
      ),
    );
  }

  void _handleOfferTap(TradingOffer offer) {
    // Navigate to trade creation or offer details
    context.push('/trading/offer/${offer.id}');
  }
}
