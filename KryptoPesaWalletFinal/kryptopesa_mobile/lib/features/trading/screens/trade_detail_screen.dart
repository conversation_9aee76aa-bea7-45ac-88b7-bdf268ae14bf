import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/loading_overlay.dart';
import '../../../core/widgets/error_display.dart';
import '../models/trading_models.dart';
import '../providers/trading_provider.dart';
import '../widgets/trade_status_indicator.dart';
import '../widgets/trade_timeline.dart';
import '../widgets/trade_actions.dart';

class TradeDetailScreen extends ConsumerStatefulWidget {
  final String tradeId;

  const TradeDetailScreen({
    super.key,
    required this.tradeId,
  });

  @override
  ConsumerState<TradeDetailScreen> createState() => _TradeDetailScreenState();
}

class _TradeDetailScreenState extends ConsumerState<TradeDetailScreen> {
  Trade? _trade;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadTradeDetails();
  }

  Future<void> _loadTradeDetails() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final trade = await ref.read(tradingProvider.notifier).getTradeById(widget.tradeId);
      
      if (mounted) {
        setState(() {
          _trade = trade;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: CustomAppBar(title: 'Trade Details'),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_error != null) {
      return Scaffold(
        appBar: CustomAppBar(title: 'Trade Details'),
        body: ErrorDisplay(
          error: _error!,
          onRetry: _loadTradeDetails,
        ),
      );
    }

    if (_trade == null) {
      return Scaffold(
        appBar: CustomAppBar(title: 'Trade Details'),
        body: const Center(
          child: Text('Trade not found'),
        ),
      );
    }

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Trade #${_trade!.id.substring(0, 8)}',
        actions: [
          IconButton(
            icon: const Icon(Icons.chat),
            onPressed: () => context.push('/trading/trade/${widget.tradeId}/chat'),
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'report',
                child: Text('Report Issue'),
              ),
              const PopupMenuItem(
                value: 'dispute',
                child: Text('Create Dispute'),
              ),
            ],
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadTradeDetails,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Trade status card
              _buildStatusCard(),
              const SizedBox(height: 16),

              // Trade details card
              _buildTradeDetailsCard(),
              const SizedBox(height: 16),

              // Counterparty info card
              _buildCounterpartyCard(),
              const SizedBox(height: 16),

              // Payment information card
              _buildPaymentInfoCard(),
              const SizedBox(height: 16),

              // Timeline card
              _buildTimelineCard(),
              const SizedBox(height: 16),

              // Actions card
              _buildActionsCard(),
              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                TradeStatusIndicator(status: _trade!.status),
                const Spacer(),
                if (_trade!.expiresAt != null)
                  _buildExpiryTimer(),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              _getStatusDescription(),
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExpiryTimer() {
    final theme = Theme.of(context);
    final now = DateTime.now();
    final expiry = _trade!.expiresAt!;
    final timeLeft = expiry.difference(now);
    
    if (timeLeft.isNegative) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          'Expired',
          style: theme.textTheme.bodySmall?.copyWith(
            color: Colors.red,
            fontWeight: FontWeight.w600,
          ),
        ),
      );
    }

    final hours = timeLeft.inHours;
    final minutes = timeLeft.inMinutes % 60;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: hours < 1 ? Colors.orange.withOpacity(0.1) : Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.timer,
            size: 16,
            color: hours < 1 ? Colors.orange : Colors.blue,
          ),
          const SizedBox(width: 4),
          Text(
            hours > 0 ? '${hours}h ${minutes}m' : '${minutes}m',
            style: theme.textTheme.bodySmall?.copyWith(
              color: hours < 1 ? Colors.orange : Colors.blue,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTradeDetailsCard() {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Trade Details',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildDetailItem(
                    'Type',
                    _trade!.isBuyTrade ? 'Buy' : 'Sell',
                    icon: _trade!.isBuyTrade ? Icons.trending_up : Icons.trending_down,
                    iconColor: _trade!.isBuyTrade ? Colors.green : Colors.red,
                  ),
                ),
                Expanded(
                  child: _buildDetailItem(
                    'Amount',
                    '${_trade!.amount.toStringAsFixed(6)} ${_trade!.cryptocurrency}',
                    icon: Icons.account_balance_wallet,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildDetailItem(
                    'Price',
                    '${_trade!.price.toStringAsFixed(2)} ${_trade!.fiatCurrency}',
                    icon: Icons.price_change,
                  ),
                ),
                Expanded(
                  child: _buildDetailItem(
                    'Total',
                    '${(_trade!.amount * _trade!.price).toStringAsFixed(2)} ${_trade!.fiatCurrency}',
                    icon: Icons.calculate,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            _buildDetailItem(
              'Trade ID',
              _trade!.id,
              icon: Icons.fingerprint,
              copyable: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCounterpartyCard() {
    final theme = Theme.of(context);
    final counterparty = _trade!.counterpartyUsername;
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Trading Partner',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                CircleAvatar(
                  radius: 24,
                  backgroundColor: theme.colorScheme.primaryContainer,
                  child: Text(
                    counterparty.isNotEmpty ? counterparty[0].toUpperCase() : 'U',
                    style: TextStyle(
                      color: theme.colorScheme.onPrimaryContainer,
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            counterparty,
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(width: 8),
                          if (_trade!.counterpartyOnline)
                            Container(
                              width: 8,
                              height: 8,
                              decoration: const BoxDecoration(
                                color: Colors.green,
                                shape: BoxShape.circle,
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            Icons.star,
                            size: 16,
                            color: Colors.amber,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _trade!.counterpartyRating.toStringAsFixed(1),
                            style: theme.textTheme.bodySmall,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '${_trade!.counterpartyTrades} trades',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                
                OutlinedButton(
                  onPressed: () => context.push('/profile/${_trade!.counterpartyId}'),
                  child: const Text('View Profile'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentInfoCard() {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Payment Information',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            _buildDetailItem(
              'Payment Method',
              _trade!.paymentMethod,
              icon: Icons.payment,
            ),
            
            if (_trade!.paymentDetails != null) ...[
              const SizedBox(height: 12),
              _buildDetailItem(
                'Payment Details',
                _trade!.paymentDetails!,
                icon: Icons.info_outline,
              ),
            ],
            
            if (_trade!.paymentReference != null) ...[
              const SizedBox(height: 12),
              _buildDetailItem(
                'Payment Reference',
                _trade!.paymentReference!,
                icon: Icons.receipt,
                copyable: true,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTimelineCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Trade Timeline',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TradeTimeline(trade: _trade!),
          ],
        ),
      ),
    );
  }

  Widget _buildActionsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Actions',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TradeActions(
              trade: _trade!,
              onAction: _handleTradeAction,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailItem(
    String label,
    String value, {
    IconData? icon,
    Color? iconColor,
    bool copyable = false,
  }) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        if (icon != null) ...[
          Icon(
            icon,
            size: 20,
            color: iconColor ?? theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 8),
        ],
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 2),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      value,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  if (copyable)
                    IconButton(
                      icon: const Icon(Icons.copy, size: 16),
                      onPressed: () => _copyToClipboard(value),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _getStatusDescription() {
    switch (_trade!.status) {
      case TradeStatus.pending:
        return 'Waiting for seller to fund escrow';
      case TradeStatus.funded:
        return 'Escrow funded, waiting for payment';
      case TradeStatus.paymentSent:
        return 'Payment sent, waiting for confirmation';
      case TradeStatus.completed:
        return 'Trade completed successfully';
      case TradeStatus.cancelled:
        return 'Trade was cancelled';
      case TradeStatus.disputed:
        return 'Trade is under dispute';
      case TradeStatus.expired:
        return 'Trade has expired';
      default:
        return 'Unknown status';
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'report':
        _showReportDialog();
        break;
      case 'dispute':
        _createDispute();
        break;
    }
  }

  void _handleTradeAction(String action) async {
    try {
      switch (action) {
        case 'fund_escrow':
          await ref.read(tradingProvider.notifier).fundEscrow(_trade!.id);
          break;
        case 'mark_payment_sent':
          await ref.read(tradingProvider.notifier).markPaymentSent(_trade!.id);
          break;
        case 'confirm_payment':
          await ref.read(tradingProvider.notifier).confirmPayment(_trade!.id);
          break;
        case 'cancel_trade':
          await ref.read(tradingProvider.notifier).cancelTrade(_trade!.id);
          break;
      }
      
      // Reload trade details
      await _loadTradeDetails();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Action completed successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Action failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showReportDialog() {
    // TODO: Implement report dialog
  }

  void _createDispute() {
    // TODO: Implement dispute creation
  }

  void _copyToClipboard(String text) {
    // TODO: Implement clipboard copy
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Copied to clipboard'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}
