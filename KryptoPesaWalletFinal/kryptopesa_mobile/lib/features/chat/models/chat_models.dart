// Chat Conversation Model
class ChatConversation {
  final String id;
  final String tradeId;
  final String participantId;
  final String participantName;
  final String? participantAvatar;
  final bool participantOnline;
  final ChatMessage? lastMessage;
  final int unreadCount;
  final DateTime createdAt;
  final DateTime updatedAt;
  final ChatStatus status;

  const ChatConversation({
    required this.id,
    required this.tradeId,
    required this.participantId,
    required this.participantName,
    this.participantAvatar,
    required this.participantOnline,
    this.lastMessage,
    required this.unreadCount,
    required this.createdAt,
    required this.updatedAt,
    required this.status,
  });

  factory ChatConversation.fromJson(Map<String, dynamic> json) {
    return ChatConversation(
      id: json['id'],
      tradeId: json['tradeId'],
      participantId: json['participantId'],
      participantName: json['participantName'],
      participantAvatar: json['participantAvatar'],
      participantOnline: json['participantOnline'] ?? false,
      lastMessage: json['lastMessage'] != null
          ? ChatMessage.fromJson(json['lastMessage'])
          : null,
      unreadCount: json['unreadCount'] ?? 0,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      status: ChatStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => ChatStatus.active,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'tradeId': tradeId,
      'participantId': participantId,
      'participantName': participantName,
      'participantAvatar': participantAvatar,
      'participantOnline': participantOnline,
      'lastMessage': lastMessage?.toJson(),
      'unreadCount': unreadCount,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'status': status.name,
    };
  }

  bool get hasUnreadMessages => unreadCount > 0;
  bool get isActive => status == ChatStatus.active;
  
  String get displayTime {
    final now = DateTime.now();
    final difference = now.difference(updatedAt);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d';
    } else {
      return '${(difference.inDays / 7).floor()}w';
    }
  }
}

// Chat Message Model
class ChatMessage {
  final String id;
  final String conversationId;
  final String senderId;
  final String senderName;
  final String content;
  final MessageType type;
  final List<MessageAttachment> attachments;
  final DateTime createdAt;
  final MessageStatus status;
  final String? replyToId;
  final ChatMessage? replyTo;

  const ChatMessage({
    required this.id,
    required this.conversationId,
    required this.senderId,
    required this.senderName,
    required this.content,
    required this.type,
    required this.attachments,
    required this.createdAt,
    required this.status,
    this.replyToId,
    this.replyTo,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'],
      conversationId: json['conversationId'],
      senderId: json['senderId'],
      senderName: json['senderName'],
      content: json['content'],
      type: MessageType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => MessageType.text,
      ),
      attachments: (json['attachments'] as List? ?? [])
          .map((a) => MessageAttachment.fromJson(a))
          .toList(),
      createdAt: DateTime.parse(json['createdAt']),
      status: MessageStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => MessageStatus.sent,
      ),
      replyToId: json['replyToId'],
      replyTo: json['replyTo'] != null
          ? ChatMessage.fromJson(json['replyTo'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'conversationId': conversationId,
      'senderId': senderId,
      'senderName': senderName,
      'content': content,
      'type': type.name,
      'attachments': attachments.map((a) => a.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'status': status.name,
      'replyToId': replyToId,
      'replyTo': replyTo?.toJson(),
    };
  }

  bool get isText => type == MessageType.text;
  bool get isImage => type == MessageType.image;
  bool get isFile => type == MessageType.file;
  bool get isSystem => type == MessageType.system;
  bool get hasAttachments => attachments.isNotEmpty;
  bool get isDelivered => status == MessageStatus.delivered;
  bool get isRead => status == MessageStatus.read;
  bool get isSent => status == MessageStatus.sent;
  bool get isPending => status == MessageStatus.pending;
  bool get isFailed => status == MessageStatus.failed;
  
  String get displayTime {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}

// Message Attachment Model
class MessageAttachment {
  final String id;
  final String name;
  final String url;
  final String? thumbnailUrl;
  final AttachmentType type;
  final int size;
  final String? mimeType;

  const MessageAttachment({
    required this.id,
    required this.name,
    required this.url,
    this.thumbnailUrl,
    required this.type,
    required this.size,
    this.mimeType,
  });

  factory MessageAttachment.fromJson(Map<String, dynamic> json) {
    return MessageAttachment(
      id: json['id'],
      name: json['name'],
      url: json['url'],
      thumbnailUrl: json['thumbnailUrl'],
      type: AttachmentType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => AttachmentType.other,
      ),
      size: json['size'],
      mimeType: json['mimeType'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'url': url,
      'thumbnailUrl': thumbnailUrl,
      'type': type.name,
      'size': size,
      'mimeType': mimeType,
    };
  }

  bool get isImage => type == AttachmentType.image;
  bool get isDocument => type == AttachmentType.document;
  
  String get displaySize {
    if (size < 1024) {
      return '${size}B';
    } else if (size < 1024 * 1024) {
      return '${(size / 1024).toStringAsFixed(1)}KB';
    } else {
      return '${(size / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
  }
}

// Typing Indicator Model
class TypingIndicator {
  final String userId;
  final String userName;
  final DateTime timestamp;

  const TypingIndicator({
    required this.userId,
    required this.userName,
    required this.timestamp,
  });

  factory TypingIndicator.fromJson(Map<String, dynamic> json) {
    return TypingIndicator(
      userId: json['userId'],
      userName: json['userName'],
      timestamp: DateTime.parse(json['timestamp']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'userName': userName,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  bool get isExpired {
    final now = DateTime.now();
    return now.difference(timestamp).inSeconds > 5;
  }
}

// Enums
enum ChatStatus {
  active,
  archived,
  blocked,
}

enum MessageType {
  text,
  image,
  file,
  system,
}

enum MessageStatus {
  pending,
  sent,
  delivered,
  read,
  failed,
}

enum AttachmentType {
  image,
  document,
  other,
}

// Chat State Model
class ChatState {
  final List<ChatConversation> conversations;
  final List<ChatMessage> messages;
  final List<TypingIndicator> typingIndicators;
  final bool isLoading;
  final bool isLoadingMessages;
  final bool isSendingMessage;
  final String? error;
  final String? currentConversationId;

  const ChatState({
    this.conversations = const [],
    this.messages = const [],
    this.typingIndicators = const [],
    this.isLoading = false,
    this.isLoadingMessages = false,
    this.isSendingMessage = false,
    this.error,
    this.currentConversationId,
  });

  bool get hasError => error != null;
  bool get hasConversations => conversations.isNotEmpty;
  bool get hasMessages => messages.isNotEmpty;
  bool get hasTypingIndicators => typingIndicators.isNotEmpty;

  ChatState copyWith({
    List<ChatConversation>? conversations,
    List<ChatMessage>? messages,
    List<TypingIndicator>? typingIndicators,
    bool? isLoading,
    bool? isLoadingMessages,
    bool? isSendingMessage,
    String? error,
    String? currentConversationId,
  }) {
    return ChatState(
      conversations: conversations ?? this.conversations,
      messages: messages ?? this.messages,
      typingIndicators: typingIndicators ?? this.typingIndicators,
      isLoading: isLoading ?? this.isLoading,
      isLoadingMessages: isLoadingMessages ?? this.isLoadingMessages,
      isSendingMessage: isSendingMessage ?? this.isSendingMessage,
      error: error,
      currentConversationId: currentConversationId ?? this.currentConversationId,
    );
  }
}

// Send Message Request
class SendMessageRequest {
  final String conversationId;
  final String content;
  final MessageType type;
  final List<String> attachmentIds;
  final String? replyToId;

  const SendMessageRequest({
    required this.conversationId,
    required this.content,
    this.type = MessageType.text,
    this.attachmentIds = const [],
    this.replyToId,
  });

  Map<String, dynamic> toJson() => {
    'conversationId': conversationId,
    'content': content,
    'type': type.name,
    'attachmentIds': attachmentIds,
    if (replyToId != null) 'replyToId': replyToId,
  };
}
