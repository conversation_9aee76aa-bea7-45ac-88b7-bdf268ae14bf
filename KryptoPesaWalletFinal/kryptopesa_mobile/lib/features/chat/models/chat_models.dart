import 'package:json_annotation/json_annotation.dart';

part 'chat_models.g.dart';

// Chat Conversation Model
@JsonSerializable()
class ChatConversation {
  final String id;
  final String tradeId;
  final String participantId;
  final String participantName;
  final String? participantAvatar;
  final bool participantOnline;
  final ChatMessage? lastMessage;
  final int unreadCount;
  final DateTime createdAt;
  final DateTime updatedAt;
  final ChatStatus status;

  const ChatConversation({
    required this.id,
    required this.tradeId,
    required this.participantId,
    required this.participantName,
    this.participantAvatar,
    required this.participantOnline,
    this.lastMessage,
    required this.unreadCount,
    required this.createdAt,
    required this.updatedAt,
    required this.status,
  });

  factory ChatConversation.fromJson(Map<String, dynamic> json) => 
      _$ChatConversationFromJson(json);
  Map<String, dynamic> toJson() => _$ChatConversationToJson(this);

  bool get hasUnreadMessages => unreadCount > 0;
  bool get isActive => status == ChatStatus.active;
  
  String get displayTime {
    final now = DateTime.now();
    final difference = now.difference(updatedAt);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d';
    } else {
      return '${(difference.inDays / 7).floor()}w';
    }
  }
}

// Chat Message Model
@JsonSerializable()
class ChatMessage {
  final String id;
  final String conversationId;
  final String senderId;
  final String senderName;
  final String content;
  final MessageType type;
  final List<MessageAttachment> attachments;
  final DateTime createdAt;
  final MessageStatus status;
  final String? replyToId;
  final ChatMessage? replyTo;

  const ChatMessage({
    required this.id,
    required this.conversationId,
    required this.senderId,
    required this.senderName,
    required this.content,
    required this.type,
    required this.attachments,
    required this.createdAt,
    required this.status,
    this.replyToId,
    this.replyTo,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) => 
      _$ChatMessageFromJson(json);
  Map<String, dynamic> toJson() => _$ChatMessageToJson(this);

  bool get isText => type == MessageType.text;
  bool get isImage => type == MessageType.image;
  bool get isFile => type == MessageType.file;
  bool get isSystem => type == MessageType.system;
  bool get hasAttachments => attachments.isNotEmpty;
  bool get isDelivered => status == MessageStatus.delivered;
  bool get isRead => status == MessageStatus.read;
  bool get isSent => status == MessageStatus.sent;
  bool get isPending => status == MessageStatus.pending;
  bool get isFailed => status == MessageStatus.failed;
  
  String get displayTime {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}

// Message Attachment Model
@JsonSerializable()
class MessageAttachment {
  final String id;
  final String name;
  final String url;
  final String? thumbnailUrl;
  final AttachmentType type;
  final int size;
  final String? mimeType;

  const MessageAttachment({
    required this.id,
    required this.name,
    required this.url,
    this.thumbnailUrl,
    required this.type,
    required this.size,
    this.mimeType,
  });

  factory MessageAttachment.fromJson(Map<String, dynamic> json) => 
      _$MessageAttachmentFromJson(json);
  Map<String, dynamic> toJson() => _$MessageAttachmentToJson(this);

  bool get isImage => type == AttachmentType.image;
  bool get isDocument => type == AttachmentType.document;
  
  String get displaySize {
    if (size < 1024) {
      return '${size}B';
    } else if (size < 1024 * 1024) {
      return '${(size / 1024).toStringAsFixed(1)}KB';
    } else {
      return '${(size / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
  }
}

// Typing Indicator Model
@JsonSerializable()
class TypingIndicator {
  final String userId;
  final String userName;
  final DateTime timestamp;

  const TypingIndicator({
    required this.userId,
    required this.userName,
    required this.timestamp,
  });

  factory TypingIndicator.fromJson(Map<String, dynamic> json) => 
      _$TypingIndicatorFromJson(json);
  Map<String, dynamic> toJson() => _$TypingIndicatorToJson(this);

  bool get isExpired {
    final now = DateTime.now();
    return now.difference(timestamp).inSeconds > 5;
  }
}

// Enums
enum ChatStatus {
  @JsonValue('active')
  active,
  @JsonValue('archived')
  archived,
  @JsonValue('blocked')
  blocked,
}

enum MessageType {
  @JsonValue('text')
  text,
  @JsonValue('image')
  image,
  @JsonValue('file')
  file,
  @JsonValue('system')
  system,
}

enum MessageStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('sent')
  sent,
  @JsonValue('delivered')
  delivered,
  @JsonValue('read')
  read,
  @JsonValue('failed')
  failed,
}

enum AttachmentType {
  @JsonValue('image')
  image,
  @JsonValue('document')
  document,
  @JsonValue('other')
  other,
}

// Chat State Model
class ChatState {
  final List<ChatConversation> conversations;
  final List<ChatMessage> messages;
  final List<TypingIndicator> typingIndicators;
  final bool isLoading;
  final bool isLoadingMessages;
  final bool isSendingMessage;
  final String? error;
  final String? currentConversationId;

  const ChatState({
    this.conversations = const [],
    this.messages = const [],
    this.typingIndicators = const [],
    this.isLoading = false,
    this.isLoadingMessages = false,
    this.isSendingMessage = false,
    this.error,
    this.currentConversationId,
  });

  bool get hasError => error != null;
  bool get hasConversations => conversations.isNotEmpty;
  bool get hasMessages => messages.isNotEmpty;
  bool get hasTypingIndicators => typingIndicators.isNotEmpty;

  ChatState copyWith({
    List<ChatConversation>? conversations,
    List<ChatMessage>? messages,
    List<TypingIndicator>? typingIndicators,
    bool? isLoading,
    bool? isLoadingMessages,
    bool? isSendingMessage,
    String? error,
    String? currentConversationId,
  }) {
    return ChatState(
      conversations: conversations ?? this.conversations,
      messages: messages ?? this.messages,
      typingIndicators: typingIndicators ?? this.typingIndicators,
      isLoading: isLoading ?? this.isLoading,
      isLoadingMessages: isLoadingMessages ?? this.isLoadingMessages,
      isSendingMessage: isSendingMessage ?? this.isSendingMessage,
      error: error,
      currentConversationId: currentConversationId ?? this.currentConversationId,
    );
  }
}

// Send Message Request
class SendMessageRequest {
  final String conversationId;
  final String content;
  final MessageType type;
  final List<String> attachmentIds;
  final String? replyToId;

  const SendMessageRequest({
    required this.conversationId,
    required this.content,
    this.type = MessageType.text,
    this.attachmentIds = const [],
    this.replyToId,
  });

  Map<String, dynamic> toJson() => {
    'conversationId': conversationId,
    'content': content,
    'type': type.name,
    'attachmentIds': attachmentIds,
    if (replyToId != null) 'replyToId': replyToId,
  };
}
