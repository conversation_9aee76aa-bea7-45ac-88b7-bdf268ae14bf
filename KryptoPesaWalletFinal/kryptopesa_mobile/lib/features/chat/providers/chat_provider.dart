import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/utils/logger.dart';
import '../models/chat_models.dart';
import '../services/chat_service.dart';

// Chat Provider
final chatProvider = StateNotifierProvider<ChatNotifier, ChatState>((ref) {
  return ChatNotifier(ref);
});

class ChatNotifier extends StateNotifier<ChatState> {
  final Ref _ref;

  ChatNotifier(this._ref) : super(const ChatState()) {
    _initializeWebSocket();
  }

  void _initializeWebSocket() {
    // TODO: Initialize WebSocket connection for real-time chat
    // final websocketService = _ref.read(websocketServiceProvider);
    // websocketService.connect();
    // websocketService.onMessage.listen(_handleWebSocketMessage);
  }

  // Load conversations
  Future<void> loadChats() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final conversations = await ChatService.getConversations();
      state = state.copyWith(
        conversations: conversations,
        isLoading: false,
      );
    } catch (e) {
      AppLogger.error('Failed to load chats', e);
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  // Load messages for a conversation
  Future<void> loadMessages(String conversationId) async {
    state = state.copyWith(
      isLoadingMessages: true,
      currentConversationId: conversationId,
      error: null,
    );

    try {
      final messages = await ChatService.getMessages(conversationId);
      state = state.copyWith(
        messages: messages,
        isLoadingMessages: false,
      );

      // Mark messages as read
      await _markMessagesAsRead(conversationId);
    } catch (e) {
      AppLogger.error('Failed to load messages', e);
      state = state.copyWith(
        isLoadingMessages: false,
        error: e.toString(),
      );
    }
  }

  // Send message
  Future<void> sendMessage(SendMessageRequest request) async {
    state = state.copyWith(isSendingMessage: true, error: null);

    try {
      // Create optimistic message
      final optimisticMessage = ChatMessage(
        id: 'temp_${DateTime.now().millisecondsSinceEpoch}',
        conversationId: request.conversationId,
        senderId: 'current_user', // TODO: Get from auth
        senderName: 'You',
        content: request.content,
        type: request.type,
        attachments: const [],
        createdAt: DateTime.now(),
        status: MessageStatus.pending,
        replyToId: request.replyToId,
      );

      // Add optimistic message to state
      final updatedMessages = [...state.messages, optimisticMessage];
      state = state.copyWith(messages: updatedMessages);

      // Send message to server
      final sentMessage = await ChatService.sendMessage(request);

      // Replace optimistic message with server response
      final finalMessages = state.messages
          .where((m) => m.id != optimisticMessage.id)
          .toList()
        ..add(sentMessage);

      state = state.copyWith(
        messages: finalMessages,
        isSendingMessage: false,
      );

      // Update conversation with new last message
      _updateConversationLastMessage(request.conversationId, sentMessage);

    } catch (e) {
      AppLogger.error('Failed to send message', e);

      // Mark optimistic message as failed
      final failedMessages = state.messages.map((m) {
        if (m.id.startsWith('temp_') && m.conversationId == request.conversationId) {
          return ChatMessage(
            id: m.id,
            conversationId: m.conversationId,
            senderId: m.senderId,
            senderName: m.senderName,
            content: m.content,
            type: m.type,
            attachments: m.attachments,
            createdAt: m.createdAt,
            status: MessageStatus.failed,
            replyToId: m.replyToId,
          );
        }
        return m;
      }).toList();

      state = state.copyWith(
        messages: failedMessages,
        isSendingMessage: false,
        error: e.toString(),
      );
    }
  }

  // Upload attachment
  Future<MessageAttachment> uploadAttachment(String filePath) async {
    try {
      return await ChatService.uploadAttachment(filePath);
    } catch (e) {
      AppLogger.error('Failed to upload attachment', e);
      rethrow;
    }
  }

  // Start typing indicator
  void startTyping(String conversationId) {
    // TODO: Send typing indicator via WebSocket
    ChatService.sendTypingIndicator(conversationId, true);
  }

  // Stop typing indicator
  void stopTyping(String conversationId) {
    // TODO: Send typing indicator via WebSocket
    ChatService.sendTypingIndicator(conversationId, false);
  }

  // Mark messages as read
  Future<void> _markMessagesAsRead(String conversationId) async {
    try {
      await ChatService.markMessagesAsRead(conversationId);

      // Update local state
      final updatedConversations = state.conversations.map((conv) {
        if (conv.id == conversationId) {
          return ChatConversation(
            id: conv.id,
            tradeId: conv.tradeId,
            participantId: conv.participantId,
            participantName: conv.participantName,
            participantAvatar: conv.participantAvatar,
            participantOnline: conv.participantOnline,
            lastMessage: conv.lastMessage,
            unreadCount: 0,
            createdAt: conv.createdAt,
            updatedAt: conv.updatedAt,
            status: conv.status,
          );
        }
        return conv;
      }).toList();

      state = state.copyWith(conversations: updatedConversations);
    } catch (e) {
      AppLogger.error('Failed to mark messages as read', e);
    }
  }

  // Update conversation with new last message
  void _updateConversationLastMessage(String conversationId, ChatMessage message) {
    final updatedConversations = state.conversations.map((conv) {
      if (conv.id == conversationId) {
        return ChatConversation(
          id: conv.id,
          tradeId: conv.tradeId,
          participantId: conv.participantId,
          participantName: conv.participantName,
          participantAvatar: conv.participantAvatar,
          participantOnline: conv.participantOnline,
          lastMessage: message,
          unreadCount: conv.unreadCount,
          createdAt: conv.createdAt,
          updatedAt: DateTime.now(),
          status: conv.status,
        );
      }
      return conv;
    }).toList();

    state = state.copyWith(conversations: updatedConversations);
  }

  // Clear current conversation
  void clearCurrentConversation() {
    state = state.copyWith(
      currentConversationId: null,
      messages: [],
      typingIndicators: [],
    );
  }
}
