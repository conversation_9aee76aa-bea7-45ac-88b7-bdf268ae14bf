import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/loading_overlay.dart';
import '../../../core/widgets/error_display.dart';
import '../../../core/widgets/empty_state.dart';
import '../models/chat_models.dart';
import '../providers/chat_provider.dart';
import '../widgets/chat_list_item.dart';

class ChatListScreen extends ConsumerStatefulWidget {
  const ChatListScreen({super.key});

  @override
  ConsumerState<ChatListScreen> createState() => _ChatListScreenState();
}

class _ChatListScreenState extends ConsumerState<ChatListScreen> {
  final _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadChats();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadChats() async {
    await ref.read(chatProvider.notifier).loadChats();
  }

  Future<void> _refreshChats() async {
    await _loadChats();
  }

  List<ChatConversation> _getFilteredChats(List<ChatConversation> chats) {
    if (_searchQuery.isEmpty) return chats;
    
    return chats.where((chat) {
      final query = _searchQuery.toLowerCase();
      return chat.participantName.toLowerCase().contains(query) ||
             chat.lastMessage?.content.toLowerCase().contains(query) == true ||
             chat.tradeId.toLowerCase().contains(query);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final chatState = ref.watch(chatProvider);
    final filteredChats = _getFilteredChats(chatState.conversations);
    
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Messages',
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar (if searching)
          if (_searchQuery.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(16),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search conversations...',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      _searchController.clear();
                      setState(() => _searchQuery = '');
                    },
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                onChanged: (value) => setState(() => _searchQuery = value),
              ),
            ),
          
          // Chat list
          Expanded(
            child: _buildChatList(chatState, filteredChats),
          ),
        ],
      ),
    );
  }

  Widget _buildChatList(ChatState state, List<ChatConversation> chats) {
    if (state.isLoading && chats.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }
    
    if (state.hasError && chats.isEmpty) {
      return ErrorDisplay(
        error: state.error!,
        onRetry: _loadChats,
      );
    }
    
    if (chats.isEmpty) {
      return EmptyState(
        icon: Icons.chat_bubble_outline,
        title: _searchQuery.isNotEmpty ? 'No matching conversations' : 'No conversations yet',
        subtitle: _searchQuery.isNotEmpty 
            ? 'Try adjusting your search terms'
            : 'Start trading to begin conversations',
        actionText: 'Browse Offers',
        onAction: () => context.go('/trading'),
      );
    }
    
    return RefreshIndicator(
      onRefresh: _refreshChats,
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: 8),
        itemCount: chats.length,
        itemBuilder: (context, index) {
          final chat = chats[index];
          return ChatListItem(
            conversation: chat,
            onTap: () => _openChat(chat),
          );
        },
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Conversations'),
        content: TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            hintText: 'Enter search terms...',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
          onSubmitted: (value) {
            setState(() => _searchQuery = value);
            Navigator.pop(context);
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() => _searchQuery = _searchController.text);
              Navigator.pop(context);
            },
            child: const Text('Search'),
          ),
        ],
      ),
    );
  }

  void _openChat(ChatConversation conversation) {
    context.push('/chat/${conversation.id}');
  }
}
