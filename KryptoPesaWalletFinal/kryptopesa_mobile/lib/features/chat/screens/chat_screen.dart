import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/loading_overlay.dart';
import '../../../core/widgets/error_display.dart';
import '../models/chat_models.dart';
import '../providers/chat_provider.dart';
import '../widgets/message_bubble.dart';
import '../widgets/message_input.dart';
import '../widgets/typing_indicator.dart';

class ChatScreen extends ConsumerStatefulWidget {
  final String conversationId;

  const ChatScreen({
    super.key,
    required this.conversationId,
  });

  @override
  ConsumerState<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends ConsumerState<ChatScreen> {
  final _scrollController = ScrollController();
  final _messageController = TextEditingController();
  Timer? _typingTimer;
  bool _isTyping = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadMessages();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _messageController.dispose();
    _typingTimer?.cancel();
    ref.read(chatProvider.notifier).clearCurrentConversation();
    super.dispose();
  }

  Future<void> _loadMessages() async {
    await ref.read(chatProvider.notifier).loadMessages(widget.conversationId);
    _scrollToBottom();
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  void _onTypingChanged(String text) {
    if (text.isNotEmpty && !_isTyping) {
      _isTyping = true;
      ref.read(chatProvider.notifier).startTyping(widget.conversationId);
    }

    _typingTimer?.cancel();
    _typingTimer = Timer(const Duration(seconds: 2), () {
      if (_isTyping) {
        _isTyping = false;
        ref.read(chatProvider.notifier).stopTyping(widget.conversationId);
      }
    });
  }

  Future<void> _sendMessage() async {
    final content = _messageController.text.trim();
    if (content.isEmpty) return;

    _messageController.clear();
    
    if (_isTyping) {
      _isTyping = false;
      ref.read(chatProvider.notifier).stopTyping(widget.conversationId);
    }

    final request = SendMessageRequest(
      conversationId: widget.conversationId,
      content: content,
      type: MessageType.text,
    );

    await ref.read(chatProvider.notifier).sendMessage(request);
    _scrollToBottom();
  }

  @override
  Widget build(BuildContext context) {
    final chatState = ref.watch(chatProvider);
    final conversation = chatState.conversations
        .where((c) => c.id == widget.conversationId)
        .firstOrNull;

    if (chatState.isLoadingMessages && chatState.messages.isEmpty) {
      return Scaffold(
        appBar: CustomAppBar(title: 'Loading...'),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (chatState.hasError && chatState.messages.isEmpty) {
      return Scaffold(
        appBar: CustomAppBar(title: 'Chat'),
        body: ErrorDisplay(
          error: chatState.error!,
          onRetry: _loadMessages,
        ),
      );
    }

    return Scaffold(
      appBar: _buildAppBar(conversation),
      body: Column(
        children: [
          // Messages list
          Expanded(
            child: _buildMessagesList(chatState),
          ),
          
          // Typing indicator
          if (chatState.hasTypingIndicators)
            TypingIndicator(
              indicators: chatState.typingIndicators,
            ),
          
          // Message input
          MessageInput(
            controller: _messageController,
            onChanged: _onTypingChanged,
            onSend: _sendMessage,
            isLoading: chatState.isSendingMessage,
          ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(ChatConversation? conversation) {
    if (conversation == null) {
      return CustomAppBar(title: 'Chat');
    }

    return CustomAppBar(
      title: conversation.participantName,
      subtitle: conversation.participantOnline ? 'Online' : 'Offline',
      leading: IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: () => context.pop(),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.info_outline),
          onPressed: () => _showChatInfo(conversation),
        ),
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'view_trade',
              child: Text('View Trade'),
            ),
            const PopupMenuItem(
              value: 'block_user',
              child: Text('Block User'),
            ),
            const PopupMenuItem(
              value: 'report',
              child: Text('Report'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMessagesList(ChatState state) {
    if (state.messages.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              'No messages yet',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Start the conversation!',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: state.messages.length,
      itemBuilder: (context, index) {
        final message = state.messages[index];
        final previousMessage = index > 0 ? state.messages[index - 1] : null;
        final nextMessage = index < state.messages.length - 1 
            ? state.messages[index + 1] 
            : null;

        final showSenderName = previousMessage?.senderId != message.senderId;
        final showTimestamp = nextMessage?.senderId != message.senderId ||
            (nextMessage != null && 
             nextMessage.createdAt.difference(message.createdAt).inMinutes > 5);

        return MessageBubble(
          message: message,
          showSenderName: showSenderName,
          showTimestamp: showTimestamp,
          onRetry: message.status == MessageStatus.failed
              ? () => ref.read(chatProvider.notifier).retryMessage(message.id)
              : null,
        );
      },
    );
  }

  void _showChatInfo(ChatConversation conversation) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.4),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 24),
            
            // User info
            CircleAvatar(
              radius: 40,
              backgroundColor: Theme.of(context).colorScheme.primaryContainer,
              backgroundImage: conversation.participantAvatar != null
                  ? NetworkImage(conversation.participantAvatar!)
                  : null,
              child: conversation.participantAvatar == null
                  ? Text(
                      conversation.participantName[0].toUpperCase(),
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    )
                  : null,
            ),
            const SizedBox(height: 16),
            
            Text(
              conversation.participantName,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            
            Text(
              conversation.participantOnline ? 'Online' : 'Offline',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: conversation.participantOnline 
                    ? Colors.green 
                    : Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 24),
            
            // Trade info
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.handshake,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Trade ID',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                        Text(
                          conversation.tradeId,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.open_in_new),
                    onPressed: () {
                      Navigator.pop(context);
                      context.push('/trading/trade/${conversation.tradeId}');
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'view_trade':
        final conversation = ref.read(chatProvider).conversations
            .where((c) => c.id == widget.conversationId)
            .firstOrNull;
        if (conversation != null) {
          context.push('/trading/trade/${conversation.tradeId}');
        }
        break;
      case 'block_user':
        _showBlockUserDialog();
        break;
      case 'report':
        _showReportDialog();
        break;
    }
  }

  void _showBlockUserDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Block User'),
        content: const Text('Are you sure you want to block this user? You will no longer receive messages from them.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement block user functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('User blocked')),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Block'),
          ),
        ],
      ),
    );
  }

  void _showReportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Report User'),
        content: const Text('Report this user for inappropriate behavior or spam.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement report user functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('User reported')),
              );
            },
            child: const Text('Report'),
          ),
        ],
      ),
    );
  }
}
