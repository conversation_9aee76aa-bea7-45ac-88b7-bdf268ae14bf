import 'dart:io';
import '../../../core/services/api_service.dart';
import '../../../core/utils/logger.dart';
import '../models/chat_models.dart';

class ChatService {
  static const String _baseUrl = '/api/chat';

  // Get conversations
  static Future<List<ChatConversation>> getConversations() async {
    try {
      // TODO: Replace with actual API call
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Mock data for now
      return [
        ChatConversation(
          id: '1',
          tradeId: 'trade_123',
          participantId: 'user_456',
          participantName: '<PERSON>',
          participantOnline: true,
          lastMessage: ChatMessage(
            id: 'msg_1',
            conversationId: '1',
            senderId: 'user_456',
            senderName: '<PERSON>',
            content: 'Payment sent! Please check your account.',
            type: MessageType.text,
            attachments: const [],
            createdAt: DateTime.now().subtract(const Duration(minutes: 5)),
            status: MessageStatus.delivered,
          ),
          unreadCount: 1,
          createdAt: DateTime.now().subtract(const Duration(hours: 2)),
          updatedAt: DateTime.now().subtract(const Duration(minutes: 5)),
          status: ChatStatus.active,
        ),
        ChatConversation(
          id: '2',
          tradeId: 'trade_789',
          participantId: 'user_101',
          participantName: 'Alice Smith',
          participantOnline: false,
          lastMessage: ChatMessage(
            id: 'msg_2',
            conversationId: '2',
            senderId: 'current_user',
            senderName: 'You',
            content: 'Thanks for the trade!',
            type: MessageType.text,
            attachments: const [],
            createdAt: DateTime.now().subtract(const Duration(hours: 1)),
            status: MessageStatus.read,
          ),
          unreadCount: 0,
          createdAt: DateTime.now().subtract(const Duration(days: 1)),
          updatedAt: DateTime.now().subtract(const Duration(hours: 1)),
          status: ChatStatus.active,
        ),
      ];
      
      // Actual implementation would be:
      // final response = await ApiService.get('$_baseUrl/conversations');
      // return (response.data as List)
      //     .map((json) => ChatConversation.fromJson(json))
      //     .toList();
    } catch (e) {
      AppLogger.error('Failed to get conversations', e);
      rethrow;
    }
  }

  // Get messages for a conversation
  static Future<List<ChatMessage>> getMessages(String conversationId) async {
    try {
      // TODO: Replace with actual API call
      await Future.delayed(const Duration(milliseconds: 300));
      
      // Mock data for now
      return [
        ChatMessage(
          id: 'msg_1',
          conversationId: conversationId,
          senderId: 'user_456',
          senderName: 'John Doe',
          content: 'Hi! I\'m interested in your USDT offer.',
          type: MessageType.text,
          attachments: const [],
          createdAt: DateTime.now().subtract(const Duration(hours: 2)),
          status: MessageStatus.read,
        ),
        ChatMessage(
          id: 'msg_2',
          conversationId: conversationId,
          senderId: 'current_user',
          senderName: 'You',
          content: 'Great! Let\'s proceed with the trade.',
          type: MessageType.text,
          attachments: const [],
          createdAt: DateTime.now().subtract(const Duration(hours: 1, minutes: 55)),
          status: MessageStatus.read,
        ),
        ChatMessage(
          id: 'msg_3',
          conversationId: conversationId,
          senderId: 'user_456',
          senderName: 'John Doe',
          content: 'Perfect! I\'ll send the payment now.',
          type: MessageType.text,
          attachments: const [],
          createdAt: DateTime.now().subtract(const Duration(hours: 1, minutes: 30)),
          status: MessageStatus.read,
        ),
        ChatMessage(
          id: 'msg_4',
          conversationId: conversationId,
          senderId: 'user_456',
          senderName: 'John Doe',
          content: 'Payment sent! Please check your account.',
          type: MessageType.text,
          attachments: const [],
          createdAt: DateTime.now().subtract(const Duration(minutes: 5)),
          status: MessageStatus.delivered,
        ),
      ];
      
      // Actual implementation would be:
      // final response = await ApiService.get('$_baseUrl/conversations/$conversationId/messages');
      // return (response.data as List)
      //     .map((json) => ChatMessage.fromJson(json))
      //     .toList();
    } catch (e) {
      AppLogger.error('Failed to get messages', e);
      rethrow;
    }
  }

  // Send message
  static Future<ChatMessage> sendMessage(SendMessageRequest request) async {
    try {
      // TODO: Replace with actual API call
      await Future.delayed(const Duration(milliseconds: 800));
      
      // Mock response
      return ChatMessage(
        id: 'msg_${DateTime.now().millisecondsSinceEpoch}',
        conversationId: request.conversationId,
        senderId: 'current_user',
        senderName: 'You',
        content: request.content,
        type: request.type,
        attachments: const [],
        createdAt: DateTime.now(),
        status: MessageStatus.sent,
        replyToId: request.replyToId,
      );
      
      // Actual implementation would be:
      // final response = await ApiService.post(
      //   '$_baseUrl/messages',
      //   data: request.toJson(),
      // );
      // return ChatMessage.fromJson(response.data);
    } catch (e) {
      AppLogger.error('Failed to send message', e);
      rethrow;
    }
  }

  // Upload attachment
  static Future<MessageAttachment> uploadAttachment(String filePath) async {
    try {
      // TODO: Replace with actual API call
      await Future.delayed(const Duration(seconds: 2));
      
      final file = File(filePath);
      final fileName = file.path.split('/').last;
      final fileSize = await file.length();
      
      // Mock response
      return MessageAttachment(
        id: 'att_${DateTime.now().millisecondsSinceEpoch}',
        name: fileName,
        url: 'https://example.com/uploads/$fileName',
        type: _getAttachmentType(fileName),
        size: fileSize,
        mimeType: _getMimeType(fileName),
      );
      
      // Actual implementation would be:
      // final formData = FormData.fromMap({
      //   'file': await MultipartFile.fromFile(filePath),
      // });
      // final response = await ApiService.post(
      //   '$_baseUrl/attachments',
      //   data: formData,
      // );
      // return MessageAttachment.fromJson(response.data);
    } catch (e) {
      AppLogger.error('Failed to upload attachment', e);
      rethrow;
    }
  }

  // Mark messages as read
  static Future<void> markMessagesAsRead(String conversationId) async {
    try {
      // TODO: Replace with actual API call
      await Future.delayed(const Duration(milliseconds: 200));
      
      // Actual implementation would be:
      // await ApiService.post('$_baseUrl/conversations/$conversationId/read');
    } catch (e) {
      AppLogger.error('Failed to mark messages as read', e);
      rethrow;
    }
  }

  // Send typing indicator
  static Future<void> sendTypingIndicator(String conversationId, bool isTyping) async {
    try {
      // TODO: Send via WebSocket instead of HTTP
      // This would typically be sent via WebSocket for real-time updates
      
      // Actual implementation would be:
      // final websocketService = GetIt.instance<WebSocketService>();
      // websocketService.send({
      //   'type': 'typing_indicator',
      //   'conversationId': conversationId,
      //   'isTyping': isTyping,
      // });
    } catch (e) {
      AppLogger.error('Failed to send typing indicator', e);
      // Don't rethrow for typing indicators as they're not critical
    }
  }

  // Get conversation by trade ID
  static Future<ChatConversation?> getConversationByTradeId(String tradeId) async {
    try {
      // TODO: Replace with actual API call
      await Future.delayed(const Duration(milliseconds: 300));
      
      // Actual implementation would be:
      // final response = await ApiService.get('$_baseUrl/conversations/trade/$tradeId');
      // return response.data != null ? ChatConversation.fromJson(response.data) : null;
      
      return null; // Mock: no conversation found
    } catch (e) {
      AppLogger.error('Failed to get conversation by trade ID', e);
      rethrow;
    }
  }

  // Create conversation for trade
  static Future<ChatConversation> createConversationForTrade(String tradeId, String participantId) async {
    try {
      // TODO: Replace with actual API call
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Mock response
      return ChatConversation(
        id: 'conv_${DateTime.now().millisecondsSinceEpoch}',
        tradeId: tradeId,
        participantId: participantId,
        participantName: 'Trading Partner',
        participantOnline: true,
        unreadCount: 0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        status: ChatStatus.active,
      );
      
      // Actual implementation would be:
      // final response = await ApiService.post(
      //   '$_baseUrl/conversations',
      //   data: {
      //     'tradeId': tradeId,
      //     'participantId': participantId,
      //   },
      // );
      // return ChatConversation.fromJson(response.data);
    } catch (e) {
      AppLogger.error('Failed to create conversation for trade', e);
      rethrow;
    }
  }

  // Helper methods
  static AttachmentType _getAttachmentType(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'webp':
        return AttachmentType.image;
      case 'pdf':
      case 'doc':
      case 'docx':
      case 'txt':
        return AttachmentType.document;
      default:
        return AttachmentType.other;
    }
  }

  static String? _getMimeType(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'pdf':
        return 'application/pdf';
      case 'txt':
        return 'text/plain';
      default:
        return null;
    }
  }
}
