import 'package:flutter/material.dart';
import '../models/chat_models.dart';

class ChatListItem extends StatelessWidget {
  final ChatConversation conversation;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;

  const ChatListItem({
    super.key,
    required this.conversation,
    this.onTap,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        onLongPress: onLongPress,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            children: [
              // Avatar with online indicator
              Stack(
                children: [
                  CircleAvatar(
                    radius: 24,
                    backgroundColor: colorScheme.primaryContainer,
                    backgroundImage: conversation.participantAvatar != null
                        ? NetworkImage(conversation.participantAvatar!)
                        : null,
                    child: conversation.participantAvatar == null
                        ? Text(
                            conversation.participantName.isNotEmpty
                                ? conversation.participantName[0].toUpperCase()
                                : 'U',
                            style: TextStyle(
                              color: colorScheme.onPrimaryContainer,
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                            ),
                          )
                        : null,
                  ),
                  if (conversation.participantOnline)
                    Positioned(
                      right: 0,
                      bottom: 0,
                      child: Container(
                        width: 14,
                        height: 14,
                        decoration: BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: colorScheme.surface,
                            width: 2,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
              
              const SizedBox(width: 12),
              
              // Chat content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Name and timestamp row
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            conversation.participantName,
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: conversation.hasUnreadMessages
                                  ? FontWeight.bold
                                  : FontWeight.w500,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Text(
                          conversation.displayTime,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: conversation.hasUnreadMessages
                                ? colorScheme.primary
                                : colorScheme.onSurfaceVariant,
                            fontWeight: conversation.hasUnreadMessages
                                ? FontWeight.w600
                                : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 4),
                    
                    // Last message and unread count row
                    Row(
                      children: [
                        Expanded(
                          child: _buildLastMessage(theme, colorScheme),
                        ),
                        if (conversation.hasUnreadMessages) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: colorScheme.primary,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            constraints: const BoxConstraints(
                              minWidth: 20,
                              minHeight: 20,
                            ),
                            child: Text(
                              conversation.unreadCount > 99
                                  ? '99+'
                                  : conversation.unreadCount.toString(),
                              style: theme.textTheme.labelSmall?.copyWith(
                                color: colorScheme.onPrimary,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ],
                    ),
                    
                    const SizedBox(height: 4),
                    
                    // Trade ID
                    Text(
                      'Trade: ${conversation.tradeId}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLastMessage(ThemeData theme, ColorScheme colorScheme) {
    if (conversation.lastMessage == null) {
      return Text(
        'No messages yet',
        style: theme.textTheme.bodyMedium?.copyWith(
          color: colorScheme.onSurfaceVariant,
          fontStyle: FontStyle.italic,
        ),
        overflow: TextOverflow.ellipsis,
      );
    }

    final message = conversation.lastMessage!;
    final isCurrentUser = message.senderId == 'current_user'; // TODO: Get from auth
    
    String displayContent;
    IconData? prefixIcon;
    
    switch (message.type) {
      case MessageType.text:
        displayContent = message.content;
        break;
      case MessageType.image:
        displayContent = 'Photo';
        prefixIcon = Icons.image;
        break;
      case MessageType.file:
        displayContent = 'File';
        prefixIcon = Icons.attach_file;
        break;
      case MessageType.system:
        displayContent = message.content;
        prefixIcon = Icons.info_outline;
        break;
    }
    
    return Row(
      children: [
        // Sender prefix for current user
        if (isCurrentUser) ...[
          Text(
            'You: ',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurfaceVariant,
              fontWeight: conversation.hasUnreadMessages
                  ? FontWeight.w600
                  : FontWeight.normal,
            ),
          ),
        ],
        
        // Message type icon
        if (prefixIcon != null) ...[
          Icon(
            prefixIcon,
            size: 16,
            color: colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 4),
        ],
        
        // Message content
        Expanded(
          child: Text(
            displayContent,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: conversation.hasUnreadMessages
                  ? colorScheme.onSurface
                  : colorScheme.onSurfaceVariant,
              fontWeight: conversation.hasUnreadMessages
                  ? FontWeight.w600
                  : FontWeight.normal,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        
        // Message status for current user
        if (isCurrentUser) ...[
          const SizedBox(width: 4),
          _buildMessageStatusIcon(message.status, colorScheme),
        ],
      ],
    );
  }

  Widget _buildMessageStatusIcon(MessageStatus status, ColorScheme colorScheme) {
    IconData icon;
    Color color;
    
    switch (status) {
      case MessageStatus.pending:
        icon = Icons.schedule;
        color = colorScheme.onSurfaceVariant;
        break;
      case MessageStatus.sent:
        icon = Icons.check;
        color = colorScheme.onSurfaceVariant;
        break;
      case MessageStatus.delivered:
        icon = Icons.done_all;
        color = colorScheme.onSurfaceVariant;
        break;
      case MessageStatus.read:
        icon = Icons.done_all;
        color = colorScheme.primary;
        break;
      case MessageStatus.failed:
        icon = Icons.error_outline;
        color = colorScheme.error;
        break;
    }
    
    return Icon(
      icon,
      size: 14,
      color: color,
    );
  }
}
