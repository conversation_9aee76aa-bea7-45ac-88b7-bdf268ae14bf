import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/chat_models.dart';

class MessageBubble extends StatelessWidget {
  final ChatMessage message;
  final bool showSenderName;
  final bool showTimestamp;
  final VoidCallback? onRetry;

  const MessageBubble({
    super.key,
    required this.message,
    this.showSenderName = false,
    this.showTimestamp = false,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isCurrentUser = message.senderId == 'current_user'; // TODO: Get from auth
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Column(
        crossAxisAlignment: isCurrentUser 
            ? CrossAxisAlignment.end 
            : CrossAxisAlignment.start,
        children: [
          // Sender name (for group chats or when sender changes)
          if (showSenderName && !isCurrentUser)
            Padding(
              padding: const EdgeInsets.only(left: 12, bottom: 4),
              child: Text(
                message.senderName,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          
          // Message bubble
          Row(
            mainAxisAlignment: isCurrentUser 
                ? MainAxisAlignment.end 
                : MainAxisAlignment.start,
            children: [
              if (!isCurrentUser) const SizedBox(width: 8),
              
              Flexible(
                child: Container(
                  constraints: BoxConstraints(
                    maxWidth: MediaQuery.of(context).size.width * 0.75,
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 10,
                  ),
                  decoration: BoxDecoration(
                    color: isCurrentUser
                        ? colorScheme.primary
                        : colorScheme.surfaceVariant,
                    borderRadius: BorderRadius.only(
                      topLeft: const Radius.circular(18),
                      topRight: const Radius.circular(18),
                      bottomLeft: Radius.circular(isCurrentUser ? 18 : 4),
                      bottomRight: Radius.circular(isCurrentUser ? 4 : 18),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Reply to message (if any)
                      if (message.replyTo != null)
                        _buildReplyPreview(theme, colorScheme, isCurrentUser),
                      
                      // Message content
                      _buildMessageContent(theme, colorScheme, isCurrentUser),
                      
                      // Message status and timestamp
                      if (isCurrentUser || showTimestamp)
                        _buildMessageFooter(theme, colorScheme, isCurrentUser),
                    ],
                  ),
                ),
              ),
              
              if (isCurrentUser) const SizedBox(width: 8),
            ],
          ),
          
          // Timestamp (when shown separately)
          if (showTimestamp && !isCurrentUser)
            Padding(
              padding: const EdgeInsets.only(left: 12, top: 4),
              child: Text(
                _formatTimestamp(message.createdAt),
                style: theme.textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildReplyPreview(ThemeData theme, ColorScheme colorScheme, bool isCurrentUser) {
    final replyMessage = message.replyTo!;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: (isCurrentUser ? colorScheme.onPrimary : colorScheme.surface)
            .withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border(
          left: BorderSide(
            color: isCurrentUser ? colorScheme.onPrimary : colorScheme.primary,
            width: 3,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            replyMessage.senderName,
            style: theme.textTheme.bodySmall?.copyWith(
              color: isCurrentUser ? colorScheme.onPrimary : colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            replyMessage.content,
            style: theme.textTheme.bodySmall?.copyWith(
              color: isCurrentUser 
                  ? colorScheme.onPrimary.withOpacity(0.8)
                  : colorScheme.onSurfaceVariant,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildMessageContent(ThemeData theme, ColorScheme colorScheme, bool isCurrentUser) {
    switch (message.type) {
      case MessageType.text:
        return _buildTextContent(theme, colorScheme, isCurrentUser);
      case MessageType.image:
        return _buildImageContent(theme, colorScheme, isCurrentUser);
      case MessageType.file:
        return _buildFileContent(theme, colorScheme, isCurrentUser);
      case MessageType.system:
        return _buildSystemContent(theme, colorScheme);
    }
  }

  Widget _buildTextContent(ThemeData theme, ColorScheme colorScheme, bool isCurrentUser) {
    return Text(
      message.content,
      style: theme.textTheme.bodyMedium?.copyWith(
        color: isCurrentUser 
            ? colorScheme.onPrimary 
            : colorScheme.onSurfaceVariant,
      ),
    );
  }

  Widget _buildImageContent(ThemeData theme, ColorScheme colorScheme, bool isCurrentUser) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (message.attachments.isNotEmpty)
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              message.attachments.first.url,
              width: 200,
              height: 150,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                width: 200,
                height: 150,
                color: colorScheme.surfaceVariant,
                child: Icon(
                  Icons.broken_image,
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
            ),
          ),
        if (message.content.isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(
            message.content,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: isCurrentUser 
                  ? colorScheme.onPrimary 
                  : colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildFileContent(ThemeData theme, ColorScheme colorScheme, bool isCurrentUser) {
    final attachment = message.attachments.isNotEmpty 
        ? message.attachments.first 
        : null;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (attachment != null)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: (isCurrentUser ? colorScheme.onPrimary : colorScheme.surface)
                  .withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.attach_file,
                  color: isCurrentUser 
                      ? colorScheme.onPrimary 
                      : colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        attachment.name,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: isCurrentUser 
                              ? colorScheme.onPrimary 
                              : colorScheme.onSurfaceVariant,
                          fontWeight: FontWeight.w500,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        attachment.displaySize,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: isCurrentUser 
                              ? colorScheme.onPrimary.withOpacity(0.7)
                              : colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        if (message.content.isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(
            message.content,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: isCurrentUser 
                  ? colorScheme.onPrimary 
                  : colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildSystemContent(ThemeData theme, ColorScheme colorScheme) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Icons.info_outline,
          size: 16,
          color: colorScheme.onSurfaceVariant,
        ),
        const SizedBox(width: 8),
        Flexible(
          child: Text(
            message.content,
            style: theme.textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurfaceVariant,
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMessageFooter(ThemeData theme, ColorScheme colorScheme, bool isCurrentUser) {
    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            DateFormat('HH:mm').format(message.createdAt),
            style: theme.textTheme.bodySmall?.copyWith(
              color: isCurrentUser 
                  ? colorScheme.onPrimary.withOpacity(0.7)
                  : colorScheme.onSurfaceVariant,
              fontSize: 11,
            ),
          ),
          
          if (isCurrentUser) ...[
            const SizedBox(width: 4),
            _buildMessageStatusIcon(colorScheme),
          ],
          
          // Retry button for failed messages
          if (message.status == MessageStatus.failed && onRetry != null) ...[
            const SizedBox(width: 8),
            GestureDetector(
              onTap: onRetry,
              child: Icon(
                Icons.refresh,
                size: 16,
                color: colorScheme.error,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMessageStatusIcon(ColorScheme colorScheme) {
    IconData icon;
    Color color;
    
    switch (message.status) {
      case MessageStatus.pending:
        icon = Icons.schedule;
        color = colorScheme.onPrimary.withOpacity(0.7);
        break;
      case MessageStatus.sent:
        icon = Icons.check;
        color = colorScheme.onPrimary.withOpacity(0.7);
        break;
      case MessageStatus.delivered:
        icon = Icons.done_all;
        color = colorScheme.onPrimary.withOpacity(0.7);
        break;
      case MessageStatus.read:
        icon = Icons.done_all;
        color = colorScheme.onPrimary;
        break;
      case MessageStatus.failed:
        icon = Icons.error_outline;
        color = colorScheme.error;
        break;
    }
    
    return Icon(
      icon,
      size: 14,
      color: color,
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inDays == 0) {
      return DateFormat('HH:mm').format(timestamp);
    } else if (difference.inDays == 1) {
      return 'Yesterday ${DateFormat('HH:mm').format(timestamp)}';
    } else if (difference.inDays < 7) {
      return DateFormat('EEE HH:mm').format(timestamp);
    } else {
      return DateFormat('MMM d, HH:mm').format(timestamp);
    }
  }
}
