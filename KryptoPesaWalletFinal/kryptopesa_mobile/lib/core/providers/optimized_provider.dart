import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/cache_service.dart';
import '../services/performance_service.dart';
import '../services/image_service.dart';
import '../utils/logger.dart';

/// Base class for optimized state notifiers with caching and performance monitoring
abstract class OptimizedStateNotifier<T> extends StateNotifier<T> {
  OptimizedStateNotifier(super.state);

  /// Cache key prefix for this provider
  String get cacheKeyPrefix;

  /// Default cache TTL in minutes
  int get defaultCacheTtl => 15;

  /// Whether to use memory cache
  bool get useMemoryCache => true;

  /// Whether to use persistent cache
  bool get usePersistentCache => true;

  /// Cache data with automatic key generation
  Future<void> cacheData(String key, dynamic data, {int? ttlMinutes}) async {
    final fullKey = '${cacheKeyPrefix}_$key';
    final ttl = ttlMinutes ?? defaultCacheTtl;

    try {
      if (useMemoryCache) {
        CacheService.instance.setMemory(fullKey, data, ttlMinutes: ttl);
      }

      if (usePersistentCache) {
        await CacheService.instance.setPersistent(fullKey, data, ttlMinutes: ttl);
      }

      AppLogger.debug('Cached data: $fullKey (TTL: ${ttl}m)');
    } catch (e) {
      AppLogger.error('Failed to cache data: $fullKey', e);
    }
  }

  /// Get cached data with automatic key generation
  Future<T?> getCachedData<T>(String key) async {
    final fullKey = '${cacheKeyPrefix}_$key';

    try {
      // Try memory cache first
      if (useMemoryCache) {
        final memoryData = CacheService.instance.getMemory<T>(fullKey);
        if (memoryData != null) {
          AppLogger.debug('Cache hit (memory): $fullKey');
          return memoryData;
        }
      }

      // Try persistent cache
      if (usePersistentCache) {
        final persistentData = await CacheService.instance.getPersistent<T>(fullKey);
        if (persistentData != null) {
          AppLogger.debug('Cache hit (persistent): $fullKey');
          
          // Promote to memory cache
          if (useMemoryCache) {
            CacheService.instance.setMemory(fullKey, persistentData);
          }
          
          return persistentData;
        }
      }

      AppLogger.debug('Cache miss: $fullKey');
      return null;
    } catch (e) {
      AppLogger.error('Failed to get cached data: $fullKey', e);
      return null;
    }
  }

  /// Execute operation with performance monitoring and caching
  Future<R> executeWithMonitoring<R>(
    String operationName,
    Future<R> Function() operation, {
    String? cacheKey,
    int? cacheTtlMinutes,
    bool useCache = true,
  }) async {
    final perfKey = '${cacheKeyPrefix}_$operationName';
    PerformanceService.instance.startTiming(perfKey);

    try {
      // Try cache first if enabled
      if (useCache && cacheKey != null) {
        final cached = await getCachedData<R>(cacheKey);
        if (cached != null) {
          PerformanceService.instance.endTiming(perfKey, {
            'source': 'cache',
            'cache_key': cacheKey,
          });
          return cached;
        }
      }

      // Execute operation
      final result = await operation();

      // Cache result if enabled
      if (useCache && cacheKey != null && result != null) {
        await cacheData(cacheKey, result, ttlMinutes: cacheTtlMinutes);
      }

      PerformanceService.instance.endTiming(perfKey, {
        'source': 'operation',
        'cache_key': cacheKey,
        'cached': useCache && cacheKey != null,
      });

      return result;
    } catch (e) {
      PerformanceService.instance.endTiming(perfKey, {
        'source': 'error',
        'error': e.toString(),
      });
      rethrow;
    }
  }

  /// Batch update state to reduce rebuilds
  void batchUpdate(void Function() updates) {
    // Store current state
    final currentState = state;
    
    try {
      updates();
    } catch (e) {
      // Restore state on error
      state = currentState;
      rethrow;
    }
  }

  /// Clear cache for this provider
  Future<void> clearCache() async {
    try {
      // This is a simplified implementation
      // In a real app, you'd track all cache keys for this provider
      AppLogger.info('Cache cleared for $cacheKeyPrefix');
    } catch (e) {
      AppLogger.error('Failed to clear cache for $cacheKeyPrefix', e);
    }
  }

  @override
  void dispose() {
    // Cleanup any resources
    super.dispose();
  }
}

/// Optimized async notifier with caching and performance monitoring
abstract class OptimizedAsyncNotifier<T> extends AsyncNotifier<T> {
  /// Cache key prefix for this provider
  String get cacheKeyPrefix;

  /// Default cache TTL in minutes
  int get defaultCacheTtl => 15;

  /// Execute async operation with caching and monitoring
  Future<T> executeWithCaching(
    String operationName,
    Future<T> Function() operation, {
    String? cacheKey,
    int? cacheTtlMinutes,
    bool useCache = true,
  }) async {
    final perfKey = '${cacheKeyPrefix}_$operationName';
    PerformanceService.instance.startTiming(perfKey);

    try {
      // Try cache first if enabled
      if (useCache && cacheKey != null) {
        final cached = await _getCachedData<T>(cacheKey);
        if (cached != null) {
          PerformanceService.instance.endTiming(perfKey, {
            'source': 'cache',
            'cache_key': cacheKey,
          });
          return cached;
        }
      }

      // Execute operation
      final result = await operation();

      // Cache result if enabled
      if (useCache && cacheKey != null && result != null) {
        await _cacheData(cacheKey, result, ttlMinutes: cacheTtlMinutes);
      }

      PerformanceService.instance.endTiming(perfKey, {
        'source': 'operation',
        'cache_key': cacheKey,
        'cached': useCache && cacheKey != null,
      });

      return result;
    } catch (e) {
      PerformanceService.instance.endTiming(perfKey, {
        'source': 'error',
        'error': e.toString(),
      });
      rethrow;
    }
  }

  Future<void> _cacheData(String key, dynamic data, {int? ttlMinutes}) async {
    final fullKey = '${cacheKeyPrefix}_$key';
    final ttl = ttlMinutes ?? defaultCacheTtl;

    try {
      CacheService.instance.setMemory(fullKey, data, ttlMinutes: ttl);
      await CacheService.instance.setPersistent(fullKey, data, ttlMinutes: ttl);
    } catch (e) {
      AppLogger.error('Failed to cache data: $fullKey', e);
    }
  }

  Future<T?> _getCachedData<T>(String key) async {
    final fullKey = '${cacheKeyPrefix}_$key';
    return await CacheService.instance.get<T>(fullKey);
  }
}

/// Mixin for providers that need periodic data refresh
mixin PeriodicRefreshMixin<T> on StateNotifier<T> {
  Timer? _refreshTimer;
  
  /// Refresh interval in seconds
  int get refreshIntervalSeconds => 30;

  /// Whether auto-refresh is enabled
  bool get autoRefreshEnabled => true;

  /// Start periodic refresh
  void startPeriodicRefresh(Future<void> Function() refreshFunction) {
    if (!autoRefreshEnabled) return;

    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(
      Duration(seconds: refreshIntervalSeconds),
      (_) async {
        try {
          await refreshFunction();
        } catch (e) {
          AppLogger.error('Periodic refresh failed', e);
        }
      },
    );

    AppLogger.debug('Started periodic refresh (${refreshIntervalSeconds}s)');
  }

  /// Stop periodic refresh
  void stopPeriodicRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
    AppLogger.debug('Stopped periodic refresh');
  }

  @override
  void dispose() {
    stopPeriodicRefresh();
    super.dispose();
  }
}

/// Mixin for providers that need debounced operations
mixin DebounceMixin<T> on StateNotifier<T> {
  final Map<String, Timer> _debounceTimers = {};

  /// Execute function with debouncing
  void debounce(
    String key,
    void Function() function, {
    Duration delay = const Duration(milliseconds: 300),
  }) {
    _debounceTimers[key]?.cancel();
    _debounceTimers[key] = Timer(delay, function);
  }

  /// Cancel debounced operation
  void cancelDebounce(String key) {
    _debounceTimers[key]?.cancel();
    _debounceTimers.remove(key);
  }

  @override
  void dispose() {
    for (final timer in _debounceTimers.values) {
      timer.cancel();
    }
    _debounceTimers.clear();
    super.dispose();
  }
}

/// Provider for managing app-wide performance settings
final performanceSettingsProvider = StateNotifierProvider<PerformanceSettingsNotifier, PerformanceSettings>((ref) {
  return PerformanceSettingsNotifier();
});

class PerformanceSettingsNotifier extends OptimizedStateNotifier<PerformanceSettings> {
  PerformanceSettingsNotifier() : super(const PerformanceSettings());

  @override
  String get cacheKeyPrefix => 'performance_settings';

  @override
  int get defaultCacheTtl => 60; // 1 hour

  Future<void> loadSettings() async {
    await executeWithMonitoring(
      'load_settings',
      () async {
        // Load from cache or defaults
        final cached = await getCachedData<PerformanceSettings>('current');
        if (cached != null) {
          state = cached;
        }
        return state;
      },
      cacheKey: 'current',
    );
  }

  Future<void> updateImageCacheSize(int maxSizeBytes) async {
    final newSettings = state.copyWith(imageCacheMaxSizeBytes: maxSizeBytes);
    state = newSettings;
    await cacheData('current', newSettings);
    
    // Apply setting immediately
    ImageService.instance.optimizeImageCache();
  }

  Future<void> updateNetworkTimeout(Duration timeout) async {
    final newSettings = state.copyWith(networkTimeoutSeconds: timeout.inSeconds);
    state = newSettings;
    await cacheData('current', newSettings);
  }

  Future<void> togglePerformanceMonitoring(bool enabled) async {
    final newSettings = state.copyWith(performanceMonitoringEnabled: enabled);
    state = newSettings;
    await cacheData('current', newSettings);
  }
}

/// Performance settings model
class PerformanceSettings {
  final int imageCacheMaxSizeBytes;
  final int networkTimeoutSeconds;
  final bool performanceMonitoringEnabled;
  final bool aggressiveCachingEnabled;
  final int maxConcurrentRequests;

  const PerformanceSettings({
    this.imageCacheMaxSizeBytes = 50 * 1024 * 1024, // 50MB
    this.networkTimeoutSeconds = 10,
    this.performanceMonitoringEnabled = true,
    this.aggressiveCachingEnabled = true,
    this.maxConcurrentRequests = 5,
  });

  PerformanceSettings copyWith({
    int? imageCacheMaxSizeBytes,
    int? networkTimeoutSeconds,
    bool? performanceMonitoringEnabled,
    bool? aggressiveCachingEnabled,
    int? maxConcurrentRequests,
  }) {
    return PerformanceSettings(
      imageCacheMaxSizeBytes: imageCacheMaxSizeBytes ?? this.imageCacheMaxSizeBytes,
      networkTimeoutSeconds: networkTimeoutSeconds ?? this.networkTimeoutSeconds,
      performanceMonitoringEnabled: performanceMonitoringEnabled ?? this.performanceMonitoringEnabled,
      aggressiveCachingEnabled: aggressiveCachingEnabled ?? this.aggressiveCachingEnabled,
      maxConcurrentRequests: maxConcurrentRequests ?? this.maxConcurrentRequests,
    );
  }
}
