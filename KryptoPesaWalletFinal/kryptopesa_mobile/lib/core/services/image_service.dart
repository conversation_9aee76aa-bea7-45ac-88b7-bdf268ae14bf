import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'cache_service.dart';
import 'performance_service.dart';
import '../utils/logger.dart';

/// Optimized image loading service with caching and performance monitoring
class ImageService {
  static ImageService? _instance;
  static ImageService get instance => _instance ??= ImageService._();
  
  ImageService._();

  final Map<String, ImageProvider> _providerCache = {};
  final Map<String, Future<ImageProvider?>> _loadingFutures = {};
  
  // Configuration
  static const int _maxCacheSize = 50;
  static const int _imageQuality = 85;
  static const Duration _networkTimeout = Duration(seconds: 10);

  /// Load image with caching and optimization
  Future<ImageProvider?> loadImage(
    String url, {
    int? width,
    int? height,
    BoxFit fit = BoxFit.cover,
    bool useCache = true,
  }) async {
    if (url.isEmpty) return null;

    // Check provider cache first
    if (useCache && _providerCache.containsKey(url)) {
      return _providerCache[url];
    }

    // Check if already loading
    if (_loadingFutures.containsKey(url)) {
      return await _loadingFutures[url];
    }

    // Start loading
    final future = _loadImageInternal(url, width: width, height: height, fit: fit);
    _loadingFutures[url] = future;

    try {
      final provider = await future;
      _loadingFutures.remove(url);
      
      if (provider != null && useCache) {
        _cacheProvider(url, provider);
      }
      
      return provider;
    } catch (e) {
      _loadingFutures.remove(url);
      AppLogger.error('Failed to load image: $url', e);
      return null;
    }
  }

  /// Load and cache image with performance monitoring
  Future<ImageProvider?> _loadImageInternal(
    String url, {
    int? width,
    int? height,
    BoxFit fit = BoxFit.cover,
  }) async {
    PerformanceService.instance.startTiming('image_load_$url');

    try {
      // Check file cache first
      final cachedFile = await CacheService.instance.getCachedImage(url);
      if (cachedFile != null) {
        PerformanceService.instance.endTiming('image_load_$url', {
          'source': 'cache',
          'url': url,
        });
        return _createOptimizedProvider(cachedFile, width: width, height: height);
      }

      // Download image
      final imageData = await _downloadImage(url);
      if (imageData == null) return null;

      // Optimize image if dimensions specified
      Uint8List optimizedData = imageData;
      if (width != null || height != null) {
        optimizedData = await _optimizeImage(imageData, width: width, height: height, fit: fit);
      }

      // Cache the optimized image
      final newCachedFile = await CacheService.instance.cacheImage(url, optimizedData);
      
      PerformanceService.instance.endTiming('image_load_$url', {
        'source': 'network',
        'url': url,
        'original_size': imageData.length,
        'optimized_size': optimizedData.length,
        'compression_ratio': (1 - optimizedData.length / imageData.length) * 100,
      });

      return newCachedFile != null
          ? _createOptimizedProvider(newCachedFile, width: width, height: height)
          : MemoryImage(optimizedData);

    } catch (e) {
      PerformanceService.instance.endTiming('image_load_$url', {
        'source': 'error',
        'url': url,
        'error': e.toString(),
      });
      rethrow;
    }
  }

  /// Download image from network
  Future<Uint8List?> _downloadImage(String url) async {
    try {
      PerformanceService.instance.startTiming('image_download_$url');
      
      final response = await http.get(
        Uri.parse(url),
        headers: {
          'User-Agent': 'KryptoPesa/1.0',
          'Accept': 'image/*',
        },
      ).timeout(_networkTimeout);

      PerformanceService.instance.endTiming('image_download_$url', {
        'status_code': response.statusCode,
        'content_length': response.contentLength,
      });

      if (response.statusCode == 200) {
        return response.bodyBytes;
      } else {
        AppLogger.warning('Failed to download image: $url (${response.statusCode})');
        return null;
      }
    } catch (e) {
      AppLogger.error('Error downloading image: $url', e);
      return null;
    }
  }

  /// Optimize image for performance
  Future<Uint8List> _optimizeImage(
    Uint8List imageData, {
    int? width,
    int? height,
    BoxFit fit = BoxFit.cover,
  }) async {
    try {
      PerformanceService.instance.startTiming('image_optimization');

      // Decode image
      final codec = await ui.instantiateImageCodec(
        imageData,
        targetWidth: width,
        targetHeight: height,
      );
      final frame = await codec.getNextFrame();
      final image = frame.image;

      // Convert to bytes with compression
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final optimizedData = byteData!.buffer.asUint8List();

      PerformanceService.instance.endTiming('image_optimization', {
        'original_size': imageData.length,
        'optimized_size': optimizedData.length,
        'target_width': width,
        'target_height': height,
      });

      image.dispose();
      return optimizedData;
    } catch (e) {
      AppLogger.error('Failed to optimize image', e);
      return imageData; // Return original if optimization fails
    }
  }

  /// Create optimized image provider
  ImageProvider _createOptimizedProvider(
    File file, {
    int? width,
    int? height,
  }) {
    if (width != null || height != null) {
      return ResizeImage(
        FileImage(file),
        width: width,
        height: height,
        allowUpscaling: false,
      );
    }
    return FileImage(file);
  }

  /// Cache image provider
  void _cacheProvider(String url, ImageProvider provider) {
    _cleanProviderCache();
    _providerCache[url] = provider;
  }

  /// Clean provider cache when it gets too large
  void _cleanProviderCache() {
    if (_providerCache.length >= _maxCacheSize) {
      // Remove oldest entries (simple FIFO)
      final keysToRemove = _providerCache.keys.take(_providerCache.length - _maxCacheSize + 1);
      for (final key in keysToRemove) {
        _providerCache.remove(key);
      }
    }
  }

  /// Preload images for better performance
  Future<void> preloadImages(List<String> urls, BuildContext context) async {
    final futures = urls.map((url) async {
      try {
        final provider = await loadImage(url);
        if (provider != null) {
          await precacheImage(provider, context);
        }
      } catch (e) {
        AppLogger.error('Failed to preload image: $url', e);
      }
    });

    await Future.wait(futures);
    AppLogger.info('Preloaded ${urls.length} images');
  }

  /// Get cached image statistics
  Future<ImageCacheStats> getStats() async {
    final cacheStats = await CacheService.instance.getStats();
    
    return ImageCacheStats(
      providerCacheSize: _providerCache.length,
      fileCacheSize: cacheStats.imageEntries,
      totalCacheSizeBytes: cacheStats.imageSizeBytes,
      activeDownloads: _loadingFutures.length,
    );
  }

  /// Clear all image caches
  Future<void> clearCache() async {
    _providerCache.clear();
    _loadingFutures.clear();
    
    // Clear Flutter's image cache
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
    
    AppLogger.info('Image caches cleared');
  }

  /// Optimize image cache settings
  void optimizeImageCache() {
    final imageCache = PaintingBinding.instance.imageCache;
    
    // Increase cache size for better performance
    imageCache.maximumSize = 100; // Number of images
    imageCache.maximumSizeBytes = 50 * 1024 * 1024; // 50MB
    
    AppLogger.info('Image cache optimized: ${imageCache.maximumSize} images, ${imageCache.maximumSizeBytes ~/ (1024 * 1024)}MB');
  }
}

/// Widget for optimized image loading with placeholder and error handling
class OptimizedImage extends StatefulWidget {
  final String? imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final bool useCache;

  const OptimizedImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.useCache = true,
  });

  @override
  State<OptimizedImage> createState() => _OptimizedImageState();
}

class _OptimizedImageState extends State<OptimizedImage> {
  ImageProvider? _imageProvider;
  bool _isLoading = true;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  @override
  void didUpdateWidget(OptimizedImage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.imageUrl != widget.imageUrl) {
      _loadImage();
    }
  }

  Future<void> _loadImage() async {
    if (widget.imageUrl == null || widget.imageUrl!.isEmpty) {
      setState(() {
        _isLoading = false;
        _hasError = true;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      final provider = await ImageService.instance.loadImage(
        widget.imageUrl!,
        width: widget.width?.toInt(),
        height: widget.height?.toInt(),
        fit: widget.fit,
        useCache: widget.useCache,
      );

      if (mounted) {
        setState(() {
          _imageProvider = provider;
          _isLoading = false;
          _hasError = provider == null;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return widget.placeholder ?? _buildDefaultPlaceholder();
    }

    if (_hasError || _imageProvider == null) {
      return widget.errorWidget ?? _buildDefaultError();
    }

    return Image(
      image: _imageProvider!,
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
      errorBuilder: (context, error, stackTrace) {
        return widget.errorWidget ?? _buildDefaultError();
      },
    );
  }

  Widget _buildDefaultPlaceholder() {
    return Container(
      width: widget.width,
      height: widget.height,
      color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
      child: const Center(
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
    );
  }

  Widget _buildDefaultError() {
    return Container(
      width: widget.width,
      height: widget.height,
      color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
      child: Icon(
        Icons.broken_image,
        color: Theme.of(context).colorScheme.onSurfaceVariant,
      ),
    );
  }
}

/// Image cache statistics
class ImageCacheStats {
  final int providerCacheSize;
  final int fileCacheSize;
  final int totalCacheSizeBytes;
  final int activeDownloads;

  ImageCacheStats({
    required this.providerCacheSize,
    required this.fileCacheSize,
    required this.totalCacheSizeBytes,
    required this.activeDownloads,
  });

  String get formattedCacheSize {
    if (totalCacheSizeBytes < 1024) return '${totalCacheSizeBytes}B';
    if (totalCacheSizeBytes < 1024 * 1024) return '${(totalCacheSizeBytes / 1024).toStringAsFixed(1)}KB';
    return '${(totalCacheSizeBytes / (1024 * 1024)).toStringAsFixed(1)}MB';
  }
}
