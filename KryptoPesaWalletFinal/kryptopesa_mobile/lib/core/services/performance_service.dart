import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../utils/logger.dart';

/// Performance monitoring service for production optimization
/// Tracks app performance metrics, memory usage, and user interactions
class PerformanceService {
  static PerformanceService? _instance;
  static PerformanceService get instance => _instance ??= PerformanceService._();
  
  PerformanceService._();

  final Map<String, PerformanceMetric> _metrics = {};
  final List<PerformanceEvent> _events = [];
  Timer? _memoryMonitorTimer;
  Timer? _reportingTimer;
  
  // Configuration
  static const int _maxEvents = 1000;
  static const int _memoryCheckIntervalSeconds = 30;
  static const int _reportingIntervalMinutes = 5;

  /// Initialize performance monitoring
  void initialize() {
    if (kDebugMode) {
      AppLogger.info('Performance monitoring initialized (Debug mode)');
      _startMemoryMonitoring();
      _startPeriodicReporting();
    }
  }

  /// Start timing a performance metric
  void startTiming(String name) {
    _metrics[name] = PerformanceMetric(
      name: name,
      startTime: DateTime.now(),
    );
  }

  /// End timing and record the metric
  void endTiming(String name, [Map<String, dynamic>? metadata]) {
    final metric = _metrics[name];
    if (metric == null) {
      AppLogger.warning('Attempted to end timing for non-existent metric: $name');
      return;
    }

    final endTime = DateTime.now();
    final duration = endTime.difference(metric.startTime);
    
    final completedMetric = metric.copyWith(
      endTime: endTime,
      duration: duration,
      metadata: metadata,
    );
    
    _metrics[name] = completedMetric;
    _recordEvent(PerformanceEvent(
      type: PerformanceEventType.timing,
      name: name,
      value: duration.inMilliseconds.toDouble(),
      timestamp: endTime,
      metadata: metadata,
    ));

    AppLogger.debug('Performance: $name took ${duration.inMilliseconds}ms');
  }

  /// Record a custom performance event
  void recordEvent(String name, double value, {
    PerformanceEventType type = PerformanceEventType.custom,
    Map<String, dynamic>? metadata,
  }) {
    _recordEvent(PerformanceEvent(
      type: type,
      name: name,
      value: value,
      timestamp: DateTime.now(),
      metadata: metadata,
    ));
  }

  /// Record screen navigation timing
  void recordScreenNavigation(String screenName, Duration loadTime) {
    recordEvent(
      'screen_load_$screenName',
      loadTime.inMilliseconds.toDouble(),
      type: PerformanceEventType.navigation,
      metadata: {'screen': screenName},
    );
  }

  /// Record API call performance
  void recordApiCall(String endpoint, Duration duration, bool success, {
    int? statusCode,
    String? error,
  }) {
    recordEvent(
      'api_call_$endpoint',
      duration.inMilliseconds.toDouble(),
      type: PerformanceEventType.network,
      metadata: {
        'endpoint': endpoint,
        'success': success,
        'status_code': statusCode,
        'error': error,
      },
    );
  }

  /// Record memory usage
  void recordMemoryUsage() {
    if (Platform.isAndroid || Platform.isIOS) {
      _getMemoryInfo().then((memoryInfo) {
        if (memoryInfo != null) {
          recordEvent(
            'memory_usage',
            memoryInfo['used']?.toDouble() ?? 0,
            type: PerformanceEventType.memory,
            metadata: memoryInfo,
          );
        }
      });
    }
  }

  /// Record frame rendering performance
  void recordFrameMetrics(Duration frameBuildTime, Duration rasterTime) {
    recordEvent(
      'frame_build_time',
      frameBuildTime.inMicroseconds.toDouble(),
      type: PerformanceEventType.rendering,
    );
    
    recordEvent(
      'frame_raster_time',
      rasterTime.inMicroseconds.toDouble(),
      type: PerformanceEventType.rendering,
    );
  }

  /// Get performance summary
  PerformanceSummary getSummary() {
    final now = DateTime.now();
    final last24Hours = now.subtract(const Duration(hours: 24));
    
    final recentEvents = _events.where((e) => e.timestamp.isAfter(last24Hours)).toList();
    
    // Calculate averages by type
    final timingEvents = recentEvents.where((e) => e.type == PerformanceEventType.timing);
    final networkEvents = recentEvents.where((e) => e.type == PerformanceEventType.network);
    final memoryEvents = recentEvents.where((e) => e.type == PerformanceEventType.memory);
    
    return PerformanceSummary(
      totalEvents: recentEvents.length,
      averageTimingMs: _calculateAverage(timingEvents.map((e) => e.value)),
      averageNetworkMs: _calculateAverage(networkEvents.map((e) => e.value)),
      currentMemoryMb: memoryEvents.isNotEmpty 
          ? memoryEvents.last.value / (1024 * 1024)
          : 0,
      slowestOperations: _getSlowOperations(recentEvents),
      errorRate: _calculateErrorRate(networkEvents),
    );
  }

  /// Get detailed metrics for a specific operation
  List<PerformanceEvent> getMetricsFor(String name) {
    return _events.where((e) => e.name == name).toList();
  }

  /// Clear all performance data
  void clearMetrics() {
    _metrics.clear();
    _events.clear();
    AppLogger.info('Performance metrics cleared');
  }

  /// Export performance data for analysis
  Map<String, dynamic> exportData() {
    return {
      'metrics': _metrics.map((key, value) => MapEntry(key, value.toJson())),
      'events': _events.map((e) => e.toJson()).toList(),
      'summary': getSummary().toJson(),
      'exported_at': DateTime.now().toIso8601String(),
    };
  }

  void _recordEvent(PerformanceEvent event) {
    _events.add(event);
    
    // Keep only recent events to prevent memory bloat
    if (_events.length > _maxEvents) {
      _events.removeRange(0, _events.length - _maxEvents);
    }
  }

  void _startMemoryMonitoring() {
    _memoryMonitorTimer = Timer.periodic(
      const Duration(seconds: _memoryCheckIntervalSeconds),
      (_) => recordMemoryUsage(),
    );
  }

  void _startPeriodicReporting() {
    _reportingTimer = Timer.periodic(
      const Duration(minutes: _reportingIntervalMinutes),
      (_) => _generatePerformanceReport(),
    );
  }

  void _generatePerformanceReport() {
    final summary = getSummary();
    AppLogger.info('Performance Report: ${summary.toString()}');
    
    // In production, this would send to analytics service
    if (kDebugMode) {
      print('=== PERFORMANCE REPORT ===');
      print('Total Events: ${summary.totalEvents}');
      print('Avg Timing: ${summary.averageTimingMs.toStringAsFixed(2)}ms');
      print('Avg Network: ${summary.averageNetworkMs.toStringAsFixed(2)}ms');
      print('Memory Usage: ${summary.currentMemoryMb.toStringAsFixed(2)}MB');
      print('Error Rate: ${(summary.errorRate * 100).toStringAsFixed(2)}%');
      print('========================');
    }
  }

  Future<Map<String, dynamic>?> _getMemoryInfo() async {
    try {
      if (Platform.isAndroid) {
        // Use method channel to get Android memory info
        const platform = MethodChannel('com.kryptopesa.wallet/memory');
        final result = await platform.invokeMethod('getMemoryInfo');
        return Map<String, dynamic>.from(result);
      } else if (Platform.isIOS) {
        // Use method channel to get iOS memory info
        const platform = MethodChannel('com.kryptopesa.wallet/memory');
        final result = await platform.invokeMethod('getMemoryInfo');
        return Map<String, dynamic>.from(result);
      }
    } catch (e) {
      AppLogger.error('Failed to get memory info', e);
    }
    return null;
  }

  double _calculateAverage(Iterable<double> values) {
    if (values.isEmpty) return 0;
    return values.reduce((a, b) => a + b) / values.length;
  }

  List<String> _getSlowOperations(List<PerformanceEvent> events) {
    final timingEvents = events
        .where((e) => e.type == PerformanceEventType.timing)
        .toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return timingEvents
        .take(5)
        .map((e) => '${e.name}: ${e.value.toStringAsFixed(0)}ms')
        .toList();
  }

  double _calculateErrorRate(Iterable<PerformanceEvent> networkEvents) {
    if (networkEvents.isEmpty) return 0;
    
    final errorCount = networkEvents
        .where((e) => e.metadata?['success'] == false)
        .length;
    
    return errorCount / networkEvents.length;
  }

  void dispose() {
    _memoryMonitorTimer?.cancel();
    _reportingTimer?.cancel();
  }
}

/// Performance metric with timing information
class PerformanceMetric {
  final String name;
  final DateTime startTime;
  final DateTime? endTime;
  final Duration? duration;
  final Map<String, dynamic>? metadata;

  PerformanceMetric({
    required this.name,
    required this.startTime,
    this.endTime,
    this.duration,
    this.metadata,
  });

  PerformanceMetric copyWith({
    String? name,
    DateTime? startTime,
    DateTime? endTime,
    Duration? duration,
    Map<String, dynamic>? metadata,
  }) {
    return PerformanceMetric(
      name: name ?? this.name,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      duration: duration ?? this.duration,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'duration_ms': duration?.inMilliseconds,
      'metadata': metadata,
    };
  }
}

/// Performance event for tracking
class PerformanceEvent {
  final PerformanceEventType type;
  final String name;
  final double value;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  PerformanceEvent({
    required this.type,
    required this.name,
    required this.value,
    required this.timestamp,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'name': name,
      'value': value,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }
}

/// Types of performance events
enum PerformanceEventType {
  timing,
  network,
  memory,
  navigation,
  rendering,
  custom,
}

/// Performance summary for reporting
class PerformanceSummary {
  final int totalEvents;
  final double averageTimingMs;
  final double averageNetworkMs;
  final double currentMemoryMb;
  final List<String> slowestOperations;
  final double errorRate;

  PerformanceSummary({
    required this.totalEvents,
    required this.averageTimingMs,
    required this.averageNetworkMs,
    required this.currentMemoryMb,
    required this.slowestOperations,
    required this.errorRate,
  });

  Map<String, dynamic> toJson() {
    return {
      'total_events': totalEvents,
      'average_timing_ms': averageTimingMs,
      'average_network_ms': averageNetworkMs,
      'current_memory_mb': currentMemoryMb,
      'slowest_operations': slowestOperations,
      'error_rate': errorRate,
    };
  }

  @override
  String toString() {
    return 'PerformanceSummary(events: $totalEvents, timing: ${averageTimingMs.toStringAsFixed(2)}ms, network: ${averageNetworkMs.toStringAsFixed(2)}ms, memory: ${currentMemoryMb.toStringAsFixed(2)}MB, errors: ${(errorRate * 100).toStringAsFixed(2)}%)';
  }
}
