/**
 * Comprehensive Monitoring System for KryptoPesa
 * Enterprise-grade monitoring, alerting, and observability
 */

const prometheus = require('prom-client');
const winston = require('winston');
const nodemailer = require('nodemailer');
const { Webhook } = require('discord-webhook-node');
const os = require('os');
const fs = require('fs').promises;
const path = require('path');

class ComprehensiveMonitoring {
  constructor(config = {}) {
    this.config = {
      environment: process.env.NODE_ENV || 'development',
      serviceName: 'KryptoPesa',
      version: '1.0.0',
      alerting: {
        email: {
          enabled: process.env.EMAIL_ALERTS_ENABLED === 'true',
          smtp: {
            host: process.env.SMTP_HOST,
            port: process.env.SMTP_PORT || 587,
            secure: false,
            auth: {
              user: process.env.SMTP_USER,
              pass: process.env.SMTP_PASS
            }
          },
          recipients: (process.env.ALERT_RECIPIENTS || '').split(',').filter(Boolean)
        },
        discord: {
          enabled: process.env.DISCORD_ALERTS_ENABLED === 'true',
          webhookUrl: process.env.DISCORD_WEBHOOK_URL
        },
        slack: {
          enabled: process.env.SLACK_ALERTS_ENABLED === 'true',
          webhookUrl: process.env.SLACK_WEBHOOK_URL
        }
      },
      ...config
    };

    this.initializeMetrics();
    this.initializeLogging();
    this.initializeAlerting();
    this.startSystemMonitoring();
  }

  initializeMetrics() {
    // Clear default metrics
    prometheus.register.clear();

    // System metrics
    this.metrics = {
      // HTTP metrics
      httpRequestDuration: new prometheus.Histogram({
        name: 'http_request_duration_seconds',
        help: 'Duration of HTTP requests in seconds',
        labelNames: ['method', 'route', 'status_code'],
        buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10]
      }),

      httpRequestTotal: new prometheus.Counter({
        name: 'http_requests_total',
        help: 'Total number of HTTP requests',
        labelNames: ['method', 'route', 'status_code']
      }),

      httpRequestsInFlight: new prometheus.Gauge({
        name: 'http_requests_in_flight',
        help: 'Number of HTTP requests currently being processed'
      }),

      // Business metrics
      activeUsers: new prometheus.Gauge({
        name: 'active_users_total',
        help: 'Number of currently active users'
      }),

      tradingVolume: new prometheus.Counter({
        name: 'trading_volume_total',
        help: 'Total trading volume',
        labelNames: ['cryptocurrency', 'type']
      }),

      walletTransactions: new prometheus.Counter({
        name: 'wallet_transactions_total',
        help: 'Total wallet transactions',
        labelNames: ['type', 'status']
      }),

      escrowTransactions: new prometheus.Counter({
        name: 'escrow_transactions_total',
        help: 'Total escrow transactions',
        labelNames: ['status']
      }),

      // Database metrics
      databaseConnections: new prometheus.Gauge({
        name: 'database_connections_active',
        help: 'Number of active database connections'
      }),

      databaseQueryDuration: new prometheus.Histogram({
        name: 'database_query_duration_seconds',
        help: 'Duration of database queries in seconds',
        labelNames: ['operation', 'table'],
        buckets: [0.01, 0.05, 0.1, 0.3, 0.5, 1, 3, 5]
      }),

      // Cache metrics
      cacheHits: new prometheus.Counter({
        name: 'cache_hits_total',
        help: 'Total cache hits',
        labelNames: ['cache_type']
      }),

      cacheMisses: new prometheus.Counter({
        name: 'cache_misses_total',
        help: 'Total cache misses',
        labelNames: ['cache_type']
      }),

      // System metrics
      systemCpuUsage: new prometheus.Gauge({
        name: 'system_cpu_usage_percent',
        help: 'System CPU usage percentage'
      }),

      systemMemoryUsage: new prometheus.Gauge({
        name: 'system_memory_usage_bytes',
        help: 'System memory usage in bytes'
      }),

      systemDiskUsage: new prometheus.Gauge({
        name: 'system_disk_usage_percent',
        help: 'System disk usage percentage'
      }),

      // Error metrics
      errorRate: new prometheus.Counter({
        name: 'errors_total',
        help: 'Total number of errors',
        labelNames: ['type', 'severity']
      }),

      // Security metrics
      securityEvents: new prometheus.Counter({
        name: 'security_events_total',
        help: 'Total security events',
        labelNames: ['event_type', 'severity']
      }),

      failedLogins: new prometheus.Counter({
        name: 'failed_logins_total',
        help: 'Total failed login attempts',
        labelNames: ['reason']
      })
    };

    // Register all metrics
    Object.values(this.metrics).forEach(metric => {
      prometheus.register.registerMetric(metric);
    });

    // Collect default metrics
    prometheus.collectDefaultMetrics({
      prefix: 'kryptopesa_',
      timeout: 5000,
      gcDurationBuckets: [0.001, 0.01, 0.1, 1, 2, 5]
    });
  }

  initializeLogging() {
    // Create logs directory
    const logsDir = path.join(__dirname, '../logs');
    fs.mkdir(logsDir, { recursive: true }).catch(console.error);

    // Configure Winston logger
    this.logger = winston.createLogger({
      level: process.env.LOG_LEVEL || 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
      ),
      defaultMeta: {
        service: this.config.serviceName,
        version: this.config.version,
        environment: this.config.environment
      },
      transports: [
        // Console transport
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple()
          )
        }),

        // File transports
        new winston.transports.File({
          filename: path.join(logsDir, 'error.log'),
          level: 'error',
          maxsize: 50 * 1024 * 1024, // 50MB
          maxFiles: 5
        }),

        new winston.transports.File({
          filename: path.join(logsDir, 'combined.log'),
          maxsize: 100 * 1024 * 1024, // 100MB
          maxFiles: 10
        }),

        // Security logs
        new winston.transports.File({
          filename: path.join(logsDir, 'security.log'),
          level: 'warn',
          maxsize: 50 * 1024 * 1024,
          maxFiles: 10
        })
      ]
    });

    // Add structured logging methods
    this.logger.business = (message, data = {}) => {
      this.logger.info(message, { category: 'business', ...data });
    };

    this.logger.security = (message, data = {}) => {
      this.logger.warn(message, { category: 'security', ...data });
      this.metrics.securityEvents.inc({ 
        event_type: data.eventType || 'unknown',
        severity: data.severity || 'medium'
      });
    };

    this.logger.performance = (message, data = {}) => {
      this.logger.info(message, { category: 'performance', ...data });
    };
  }

  initializeAlerting() {
    // Email alerting
    if (this.config.alerting.email.enabled) {
      this.emailTransporter = nodemailer.createTransporter(this.config.alerting.email.smtp);
    }

    // Discord alerting
    if (this.config.alerting.discord.enabled) {
      this.discordWebhook = new Webhook(this.config.alerting.discord.webhookUrl);
    }

    // Alert thresholds
    this.alertThresholds = {
      cpuUsage: 80,
      memoryUsage: 85,
      diskUsage: 90,
      errorRate: 0.05, // 5%
      responseTime: 2000, // 2 seconds
      failedLogins: 10 // per minute
    };

    // Alert cooldowns (prevent spam)
    this.alertCooldowns = new Map();
  }

  startSystemMonitoring() {
    // Monitor system resources every 30 seconds
    setInterval(() => {
      this.collectSystemMetrics();
    }, 30000);

    // Monitor application health every minute
    setInterval(() => {
      this.performHealthChecks();
    }, 60000);

    // Check alert conditions every 30 seconds
    setInterval(() => {
      this.checkAlertConditions();
    }, 30000);
  }

  collectSystemMetrics() {
    try {
      // CPU usage
      const cpus = os.cpus();
      let totalIdle = 0;
      let totalTick = 0;

      cpus.forEach(cpu => {
        for (const type in cpu.times) {
          totalTick += cpu.times[type];
        }
        totalIdle += cpu.times.idle;
      });

      const cpuUsage = 100 - (totalIdle / totalTick * 100);
      this.metrics.systemCpuUsage.set(cpuUsage);

      // Memory usage
      const totalMemory = os.totalmem();
      const freeMemory = os.freemem();
      const usedMemory = totalMemory - freeMemory;
      this.metrics.systemMemoryUsage.set(usedMemory);

      // Log system metrics
      this.logger.performance('System metrics collected', {
        cpuUsage: cpuUsage.toFixed(2),
        memoryUsage: ((usedMemory / totalMemory) * 100).toFixed(2),
        freeMemory: (freeMemory / 1024 / 1024 / 1024).toFixed(2) + 'GB'
      });

    } catch (error) {
      this.logger.error('Failed to collect system metrics', { error: error.message });
    }
  }

  async performHealthChecks() {
    const healthChecks = {
      database: await this.checkDatabaseHealth(),
      cache: await this.checkCacheHealth(),
      externalServices: await this.checkExternalServices(),
      diskSpace: await this.checkDiskSpace()
    };

    const overallHealth = Object.values(healthChecks).every(check => check.healthy);

    this.logger.info('Health check completed', {
      overall: overallHealth ? 'healthy' : 'unhealthy',
      checks: healthChecks
    });

    if (!overallHealth) {
      await this.sendAlert('HEALTH_CHECK_FAILED', {
        message: 'System health check failed',
        details: healthChecks,
        severity: 'high'
      });
    }

    return healthChecks;
  }

  async checkDatabaseHealth() {
    try {
      // This would check actual database connection
      return { healthy: true, responseTime: 50 };
    } catch (error) {
      return { healthy: false, error: error.message };
    }
  }

  async checkCacheHealth() {
    try {
      // This would check Redis connection
      return { healthy: true, responseTime: 10 };
    } catch (error) {
      return { healthy: false, error: error.message };
    }
  }

  async checkExternalServices() {
    // Check external service dependencies
    return { healthy: true, services: [] };
  }

  async checkDiskSpace() {
    try {
      const stats = await fs.stat('/');
      // Simplified disk space check
      return { healthy: true, usage: 45 };
    } catch (error) {
      return { healthy: false, error: error.message };
    }
  }

  checkAlertConditions() {
    // Check CPU usage
    const cpuUsage = this.metrics.systemCpuUsage.get();
    if (cpuUsage > this.alertThresholds.cpuUsage) {
      this.triggerAlert('HIGH_CPU_USAGE', {
        message: `CPU usage is ${cpuUsage.toFixed(2)}%`,
        threshold: this.alertThresholds.cpuUsage,
        current: cpuUsage,
        severity: 'medium'
      });
    }

    // Check memory usage
    const memoryUsage = (this.metrics.systemMemoryUsage.get() / os.totalmem()) * 100;
    if (memoryUsage > this.alertThresholds.memoryUsage) {
      this.triggerAlert('HIGH_MEMORY_USAGE', {
        message: `Memory usage is ${memoryUsage.toFixed(2)}%`,
        threshold: this.alertThresholds.memoryUsage,
        current: memoryUsage,
        severity: 'medium'
      });
    }
  }

  async triggerAlert(alertType, alertData) {
    const cooldownKey = `${alertType}_${alertData.severity}`;
    const now = Date.now();
    const cooldownPeriod = 5 * 60 * 1000; // 5 minutes

    // Check cooldown
    if (this.alertCooldowns.has(cooldownKey)) {
      const lastAlert = this.alertCooldowns.get(cooldownKey);
      if (now - lastAlert < cooldownPeriod) {
        return; // Skip alert due to cooldown
      }
    }

    // Set cooldown
    this.alertCooldowns.set(cooldownKey, now);

    // Log alert
    this.logger.warn('Alert triggered', {
      alertType,
      ...alertData,
      timestamp: new Date().toISOString()
    });

    // Send alert notifications
    await this.sendAlert(alertType, alertData);
  }

  async sendAlert(alertType, alertData) {
    const alert = {
      type: alertType,
      service: this.config.serviceName,
      environment: this.config.environment,
      timestamp: new Date().toISOString(),
      ...alertData
    };

    // Send email alert
    if (this.config.alerting.email.enabled && this.emailTransporter) {
      await this.sendEmailAlert(alert);
    }

    // Send Discord alert
    if (this.config.alerting.discord.enabled && this.discordWebhook) {
      await this.sendDiscordAlert(alert);
    }
  }

  async sendEmailAlert(alert) {
    try {
      const subject = `🚨 ${alert.service} Alert: ${alert.type}`;
      const html = this.generateAlertEmailHTML(alert);

      await this.emailTransporter.sendMail({
        from: process.env.SMTP_FROM || '<EMAIL>',
        to: this.config.alerting.email.recipients,
        subject,
        html
      });

      this.logger.info('Email alert sent', { alertType: alert.type });
    } catch (error) {
      this.logger.error('Failed to send email alert', { error: error.message });
    }
  }

  async sendDiscordAlert(alert) {
    try {
      const embed = {
        title: `🚨 ${alert.service} Alert`,
        description: alert.message,
        color: this.getAlertColor(alert.severity),
        fields: [
          { name: 'Type', value: alert.type, inline: true },
          { name: 'Environment', value: alert.environment, inline: true },
          { name: 'Severity', value: alert.severity.toUpperCase(), inline: true },
          { name: 'Timestamp', value: alert.timestamp, inline: false }
        ],
        footer: { text: 'KryptoPesa Monitoring System' }
      };

      if (alert.details) {
        embed.fields.push({
          name: 'Details',
          value: JSON.stringify(alert.details, null, 2),
          inline: false
        });
      }

      await this.discordWebhook.send({ embeds: [embed] });
      this.logger.info('Discord alert sent', { alertType: alert.type });
    } catch (error) {
      this.logger.error('Failed to send Discord alert', { error: error.message });
    }
  }

  generateAlertEmailHTML(alert) {
    return `
      <html>
        <body style="font-family: Arial, sans-serif; margin: 20px;">
          <div style="background-color: ${this.getAlertBackgroundColor(alert.severity)}; padding: 20px; border-radius: 5px;">
            <h2 style="color: #333; margin-top: 0;">🚨 ${alert.service} Alert</h2>
            <p><strong>Type:</strong> ${alert.type}</p>
            <p><strong>Message:</strong> ${alert.message}</p>
            <p><strong>Severity:</strong> ${alert.severity.toUpperCase()}</p>
            <p><strong>Environment:</strong> ${alert.environment}</p>
            <p><strong>Timestamp:</strong> ${alert.timestamp}</p>
            ${alert.details ? `<p><strong>Details:</strong><br><pre>${JSON.stringify(alert.details, null, 2)}</pre></p>` : ''}
          </div>
          <p style="margin-top: 20px; font-size: 12px; color: #666;">
            This alert was generated by the KryptoPesa monitoring system.
          </p>
        </body>
      </html>
    `;
  }

  getAlertColor(severity) {
    const colors = {
      low: 0x00ff00,      // Green
      medium: 0xffff00,   // Yellow
      high: 0xff8000,     // Orange
      critical: 0xff0000  // Red
    };
    return colors[severity] || colors.medium;
  }

  getAlertBackgroundColor(severity) {
    const colors = {
      low: '#d4edda',
      medium: '#fff3cd',
      high: '#ffeaa7',
      critical: '#f8d7da'
    };
    return colors[severity] || colors.medium;
  }

  // Express middleware for HTTP metrics
  getHTTPMetricsMiddleware() {
    return (req, res, next) => {
      const start = Date.now();
      this.metrics.httpRequestsInFlight.inc();

      res.on('finish', () => {
        const duration = (Date.now() - start) / 1000;
        const route = req.route?.path || req.path;

        this.metrics.httpRequestDuration
          .labels(req.method, route, res.statusCode)
          .observe(duration);

        this.metrics.httpRequestTotal
          .labels(req.method, route, res.statusCode)
          .inc();

        this.metrics.httpRequestsInFlight.dec();

        // Log slow requests
        if (duration > 2) {
          this.logger.performance('Slow request detected', {
            method: req.method,
            path: req.path,
            duration: duration.toFixed(3),
            statusCode: res.statusCode,
            userAgent: req.get('User-Agent'),
            ip: req.ip
          });
        }
      });

      next();
    };
  }

  // Business metrics methods
  recordTradingVolume(cryptocurrency, type, amount) {
    this.metrics.tradingVolume.labels(cryptocurrency, type).inc(amount);
    this.logger.business('Trading volume recorded', {
      cryptocurrency,
      type,
      amount
    });
  }

  recordWalletTransaction(type, status) {
    this.metrics.walletTransactions.labels(type, status).inc();
    this.logger.business('Wallet transaction recorded', { type, status });
  }

  recordEscrowTransaction(status) {
    this.metrics.escrowTransactions.labels(status).inc();
    this.logger.business('Escrow transaction recorded', { status });
  }

  recordSecurityEvent(eventType, severity, details = {}) {
    this.metrics.securityEvents.labels(eventType, severity).inc();
    this.logger.security('Security event recorded', {
      eventType,
      severity,
      ...details
    });

    // Trigger alert for high severity security events
    if (severity === 'high' || severity === 'critical') {
      this.triggerAlert('SECURITY_EVENT', {
        message: `Security event: ${eventType}`,
        eventType,
        severity,
        details
      });
    }
  }

  recordFailedLogin(reason, details = {}) {
    this.metrics.failedLogins.labels(reason).inc();
    this.logger.security('Failed login attempt', {
      reason,
      ...details
    });
  }

  // Metrics endpoint for Prometheus
  getMetricsEndpoint() {
    return async (req, res) => {
      try {
        res.set('Content-Type', prometheus.register.contentType);
        res.end(await prometheus.register.metrics());
      } catch (error) {
        res.status(500).end(error.message);
      }
    };
  }

  // Health endpoint
  getHealthEndpoint() {
    return async (req, res) => {
      try {
        const health = await this.performHealthChecks();
        const overall = Object.values(health).every(check => check.healthy);

        res.status(overall ? 200 : 503).json({
          status: overall ? 'healthy' : 'unhealthy',
          timestamp: new Date().toISOString(),
          service: this.config.serviceName,
          version: this.config.version,
          checks: health
        });
      } catch (error) {
        res.status(500).json({
          status: 'error',
          error: error.message
        });
      }
    };
  }
}

module.exports = ComprehensiveMonitoring;
